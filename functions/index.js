const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp({
  credential: admin.credential.applicationDefault(),
  storageBucket: 'bookbox-5153e.appspot.com'
});

//Import your function from the specific file
const { updateFileAndAggregateStorage } = require('./storage/updateStorageOnDiscussionFilesUpload');
const { updateStorageOnFilesUpload } = require('./storage/updateStorageOnFilesUpload');
const { hasEnoughStorage } = require('./storage/hasEnoughStorage');
const { ensureNonNegativeStorage } = require('./storage/ensureNonNegativeStorage');
const { deleteDiscussions } = require('./deletion/deleteDiscussions');
const { deleteFiles } = require('./deletion/deleteFiles');
const { deleteProject } = require('./deletion/deleteProject');
const { deleteMultipleProjects } = require('./deletion/deleteMultipleProjects');
const { deleteUser } = require('./deletion/deleteUser');
const { submitApplicationQuestions } = require('./applications/submitApplicationQuestions');
const { acceptUserApplication } = require('./applications/acceptUserApplication');
const { denyUserApplication } = require('./applications/denyUserApplication');


// Export the function
exports.updateFileAndAggregateStorage = updateFileAndAggregateStorage;
exports.updateStorageOnFilesUpload = updateStorageOnFilesUpload;
exports.hasEnoughStorage = hasEnoughStorage;
exports.ensureNonNegativeStorage = ensureNonNegativeStorage;
exports.deleteDiscussions = deleteDiscussions;
exports.deleteFiles = deleteFiles;
exports.deleteProject = deleteProject;
exports.deleteMultipleProjects = deleteMultipleProjects;
exports.deleteUser = deleteUser;
exports.submitApplicationQuestions = submitApplicationQuestions;
exports.acceptUserApplication = acceptUserApplication;
exports.denyUserApplication = denyUserApplication;