const functions = require('firebase-functions');
const admin = require('firebase-admin');


// updates storage for 'discussions', 'users', 'projects', and 'discussionFiles' collections
// whenver a file is uploaded to the 'discussionFiles' storage bucket
// when a new discusion is created
// it is triggered by uploads to the 'discussionFiles/' storage bucket

// this function does not handle uploads to 'database' or 'e-publishing'
exports.updateFileAndAggregateStorage = functions.storage.object().onFinalize(async (object) => {
    console.log("Object:", object.name, "Metadata:", object.metadata); // Log file name and metadata

    if (object.name.startsWith('discussionFiles/')) {
        const fileSize = Number(object.size);
        const metadata = object.metadata;
        const userId = metadata.userId;
        const projectId = metadata.projectId;
        const discussionId = metadata.discussionId;
        const fileDocumentId = metadata.fileDocumentId;
        const fileUrl = await admin.storage().bucket().file(object.name).getSignedUrl({
            action: 'read',
            expires: '03-01-2500', // Far future date
        }).then(urls => urls[0]);

        if (!userId || !discussionId || !fileDocumentId) {
            console.error("Missing metadata:", metadata);
            return null; // Exit if any metadata is missing
        }

        const db = admin.firestore();
        const discussionFileRef = db.collection('discussionFiles').doc(fileDocumentId);
        const discussionRef = db.collection('discussions').doc(discussionId);
        const userRef = db.collection('users').doc(userId);
        const projectRef = db.collection('projects').doc(projectId);

        try {
            // Check if this file already exists in the 'data' collection
            const existingDataQuery = await db.collection('data')
                .where('File URL', '==', fileUrl)
                .get();

            // Check if this file already exists in the 'discussionFiles' collection (excluding current document)
            const existingDiscussionFilesQuery = await db.collection('discussionFiles')
                .where('fileUrl', '==', fileUrl)
                .get();

            // Determine if this file is already uploaded somewhere else
            const isFileAlreadyUploaded = existingDataQuery.size > 0 ||
                (existingDiscussionFilesQuery.size > 0 &&
                    existingDiscussionFilesQuery.docs.some(doc => doc.id !== fileDocumentId));

            await db.runTransaction(async (transaction) => {
                const discussionDoc = await transaction.get(discussionRef);
                const userDoc = await transaction.get(userRef);
                const projectDoc = await transaction.get(projectRef);

                if (!discussionDoc.exists || !userDoc.exists) {
                    throw new Error("Document does not exist");
                }

                const currentDiscussionStorage = Number(discussionDoc.data()['Storage Used'] || 0);
                const currentUserStorage = Number(userDoc.data()['Storage Used'] || 0);
                const currentProjectStorage = Number(projectDoc.data()['Storage Used'] || 0);

                // Always update the file size in the discussionFile document
                transaction.update(discussionFileRef, { fileSize: fileSize, fileUrl: fileUrl });

                // Only update storage usage if this is a new file (not already uploaded elsewhere)
                if (!isFileAlreadyUploaded) {
                    console.log(`Updating storage for new file: ${fileUrl}`);
                    transaction.update(discussionRef, { 'Storage Used': currentDiscussionStorage + fileSize });
                    transaction.update(userRef, { 'Storage Used': currentUserStorage + fileSize });
                    transaction.update(projectRef, { 'Storage Used': currentProjectStorage + fileSize });
                } else {
                    console.log(`File already exists, not updating storage: ${fileUrl}`);
                }
            });

            console.log(`Storage processed for file ${fileDocumentId}, discussion ${discussionId}, and user ${userId}`);
        } catch (error) {
            console.error('Transaction failure:', error);
            throw new functions.https.HttpsError('internal', 'Transaction failure, unable to update storage information.');
        }
    }

    return null;
});

