const functions = require('firebase-functions');
const admin = require('firebase-admin');

// This function ensures that storage values are never negative
// It can be called manually or scheduled to run periodically
exports.ensureNonNegativeStorage = functions.https.onCall(async (data, context) => {
    // Check for authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }

    const db = admin.firestore();
    const batch = db.batch();
    let fixedCount = 0;

    try {
        // Check users collection
        const usersSnapshot = await db.collection('users').get();
        for (const doc of usersSnapshot.docs) {
            const storageUsed = doc.data()['Storage Used'];
            if (storageUsed < 0) {
                console.log(`Fixing negative storage for user ${doc.id}: ${storageUsed} -> 0`);
                batch.update(doc.ref, { 'Storage Used': 0 });
                fixedCount++;
            }
        }

        // Check projects collection
        const projectsSnapshot = await db.collection('projects').get();
        for (const doc of projectsSnapshot.docs) {
            const storageUsed = doc.data()['Storage Used'];
            if (storageUsed < 0) {
                console.log(`Fixing negative storage for project ${doc.id}: ${storageUsed} -> 0`);
                batch.update(doc.ref, { 'Storage Used': 0 });
                fixedCount++;
            }
        }

        // Commit the batch if there are any changes
        if (fixedCount > 0) {
            await batch.commit();
            console.log(`Fixed ${fixedCount} documents with negative storage values`);
        } else {
            console.log('No negative storage values found');
        }

        return { 
            success: true, 
            message: `Fixed ${fixedCount} documents with negative storage values` 
        };
    } catch (error) {
        console.error('Error fixing negative storage values:', error);
        throw new functions.https.HttpsError('internal', 'Error fixing negative storage values', error);
    }
});
