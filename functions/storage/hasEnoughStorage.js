const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.hasEnoughStorage = functions.https.onCall(async (data, context) => {
    // Check for authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }

    const db = admin.firestore();

    // Data passed to the function includes fileSize and userId
    const { fileSize, userId } = data;

    try {
        // Get reference to the user's document
        const userRef = db.collection('users').doc(userId);
        const userDoc = await userRef.get();

        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User document does not exist');
        }

        const currentUserStorage = Number(userDoc.data()['Storage Used'] || 0);
        const userStorageLimit = Number(userDoc.data()['Storage Limit'] || 0); // Assuming you have a 'Storage Limit' field

        // Check if the user has enough space for the new file
        if (currentUserStorage + parseInt(fileSize) > userStorageLimit) {
            return { success: false, message: 'Not enough storage space available. Please free up space before proceeding.' };
        }

        return { success: true, message: 'Sufficient storage space available.' };

    } catch (error) {
        console.error('Error checking storage:', error);
        throw new functions.https.HttpsError('internal', 'Error checking storage space.');
    }
});