const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Updates storage for 'data', 'projects', and 'users' collections
// This function is triggered via HTTP after a file upload is confirmed and metadata is ready
exports.updateStorageOnFilesUpload = functions.https.onCall(async (data, context) => {
    // Check for authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'The function must be called while authenticated.');
    }

    const db = admin.firestore();

    // Data passed to the function includes fileSize, userId, fileUrl, projectId
    const { fileSize, userId, fileUrl, projectId } = data;

    try {
        // Get references to the 3 collections we're updating
        const userRef = db.collection('users').doc(userId);
        const projectRef = db.collection('projects').doc(projectId);

        // First, check if this file URL already exists in other documents
        const existingFileQuery = await db.collection('data')
            .where('File URL', '==', fileUrl)
            .get();

        // Check if this file already exists in the 'discussionFiles' collection
        const existingDiscussionFilesQuery = await db.collection('discussionFiles')
            .where('fileUrl', '==', fileUrl)
            .get();

        // Get the current document ID we're working with
        const dataSnapshot = await db.collection('data')
            .where('File URL', '==', fileUrl)
            .limit(1)
            .get();

        if (dataSnapshot.empty) {
            throw new Error("No matching data document found");
        }

        const currentDocId = dataSnapshot.docs[0].id;

        // If the file already exists elsewhere:
        // 1. More than 1 document in data collection (and at least one is not the current document)
        // 2. Or exists in discussionFiles collection
        const isFileAlreadyUploaded =
            (existingFileQuery.size > 1 ||
                (existingFileQuery.size === 1 && existingFileQuery.docs[0].id !== currentDocId)) ||
            existingDiscussionFilesQuery.size > 0;

        // Use the document we already retrieved
        const dataDoc = dataSnapshot.docs[0];
        const dataRef = db.collection('data').doc(dataDoc.id);

        await db.runTransaction(async (transaction) => {
            const userDoc = await transaction.get(userRef);
            const projectDoc = await transaction.get(projectRef);
            const dataDocument = await transaction.get(dataRef);

            if (!projectDoc.exists || !userDoc.exists || !dataDocument.exists) {
                throw new Error("Project, user, or data document does not exist");
            }

            const currentUserStorage = Number(userDoc.data()['Storage Used'] || 0);
            const currentProjectStorage = Number(projectDoc.data()['Storage Used'] || 0);

            // Ensure we're using the parsed fileSize value
            const parsedFileSize = parseInt(fileSize);

            // Only update storage usage if this is a new file (not already uploaded elsewhere)
            if (!isFileAlreadyUploaded) {
                console.log(`Updating storage for new file: ${fileUrl}`);
                // Update user and project storage by adding the file size
                transaction.update(userRef, { 'Storage Used': Math.max(0, currentUserStorage + parsedFileSize) });
                transaction.update(projectRef, { 'Storage Used': Math.max(0, currentProjectStorage + parsedFileSize) });
            } else {
                console.log(`File already exists, not updating storage: ${fileUrl}`);
            }

            // Set the file size directly instead of adding to it
            transaction.update(dataRef, { 'File Size': parsedFileSize });
        });

    } catch (error) {
        console.error('Transaction failure:', error);
        throw new functions.https.HttpsError('internal', 'Transaction failure, unable to update storage information.');
    }

    return { result: 'Storage updated successfully' };
});

