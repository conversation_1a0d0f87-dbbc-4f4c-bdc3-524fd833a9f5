const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.submitApplicationQuestions = functions.https.onCall(async (data, context) => {
    // Check if the user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated to submit applications.');
    }

    const { items, projectID, displayName } = data;

    // Ensure required data is present
    if (!projectID || !items || items.length === 0) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required fields.');
    }

    const uid = context.auth.uid;
    const db = admin.firestore();

    // Check if a submission already exists
    const existingSubmissions = await db.collection('applications')
        .where('uid', '==', uid)
        .where('projectID', '==', projectID)
        .get();

    if (!existingSubmissions.empty) {
        console.log('Submission already exists for this user and project.');
        return { result: 'Submission already exists.' };
    }

    // Get project owner
    const projectRef = db.collection('projects').doc(projectID);
    const projectDoc = await projectRef.get();
    if (!projectDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Project not found.');
    }
    const projectOwner = projectDoc.data().uid;
    const projectName = projectDoc.data()['Project Name']; // Corrected to match the field name in the database

    // Prepare data to write
    const itemsMap = items.map(item => ({
        order: item.order,
        question: item.question,
        answer: item.answer
    }));

    // Create a new document in the 'applications' collection
    const documentRef = db.collection('applications').doc();
    const now = admin.firestore.Timestamp.now();

    const submissionData = {
        items: itemsMap, // done
        uid: uid, // done
        DisplayName: displayName, // done
        timeStamp: now, // done
        projectID: projectID, // done
        projectOwner: projectOwner, // done
        projectName: projectName, // done
        status: 'pending', // done
        applicationID: documentRef.id
    };

    // Write the new submission to Firestore
    await documentRef.set(submissionData);
    console.log('Document successfully written with an array of items and additional fields.');

    return { result: 'Application submitted successfully.', id: documentRef.id };
});