const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.denyUserApplication = functions.https.onCall(async (data, context) => {

    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated to submit applications.');
    }

    const { applicationID } = data;

    const db = admin.firestore();
    const applicationRef = db.collection('applications').doc(applicationID);
    await applicationRef.delete();

});