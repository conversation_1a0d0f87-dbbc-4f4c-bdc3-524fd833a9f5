// const functions = require('firebase-functions');
// const admin = require('firebase-admin');

// exports.acceptUserApplication = functions.https.onCall(async (data, context) => {
//     if (!context.auth) {
//         throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated to submit applications.');
//     }

//     const uid = context.auth.uid;
//     const db = admin.firestore();

//     const { applicationID, projectID } = data;

//     const applicationRef = db.collection('applications').doc(applicationID);
//     const applicationDoc = await applicationRef.get();

//     // subscribe user to project

//     if (!applicationDoc.exists) {
//         await applicationRef.set({
//             projectID: true
//         });

//     } else {
//         await applicationRef.update({
//             projectID: true
//         });
//     }

//     // fetch UID of applicant
//     const applicationData = applicationRef.where('applicationID', '==', applicationID).get();
//     const applicantUID = applicationData.uid;

//     // update subscription value within project document
//     const projectDocRef = db.collection('projects').doc(projectID);
//     await projectDocRef.update({
//         'Subscribers': admin.firestore.FieldValue.increment(1),
//     });

//     // delete application
//     await db.collection("applications").doc(applicationID).delete();

// });


const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.acceptUserApplication = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated to submit applications.');
    }

    const uid = context.auth.uid;
    const db = admin.firestore();

    const { applicationID, projectID } = data;

    const applicationRef = db.collection('applications').doc(applicationID);
    const applicationDoc = await applicationRef.get();

    if (!applicationDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Application not found.');
    }

    // Fetch UID of the applicant from the application document
    const applicantUID = applicationDoc.data().uid;

    // Subscribe the user to the project
    await applicationRef.update({
        projectID: true
    });

    // Update subscription value within project document
    const projectDocRef = db.collection('projects').doc(projectID);
    await projectDocRef.update({
        'Subscribers': admin.firestore.FieldValue.increment(1),
    });

    const projectSubscriptionsRef = db.collection('projectSubscriptions').doc(applicantUID);
    await projectSubscriptionsRef.set({
        [projectID]: true
    }, { merge: true });

    // Delete the application
    await applicationRef.delete();

    return { message: 'Application accepted and processed successfully.' };
});