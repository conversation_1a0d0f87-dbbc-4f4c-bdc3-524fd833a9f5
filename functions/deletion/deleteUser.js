const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.deleteUser = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        console.error('Authentication error: User must be authenticated.');
        throw new functions.https.HttpsError('permission-denied', 'User must be authenticated.');
    }

    const db = admin.firestore();
    const userID = data.userID;

    // Fetch and delete related documents in these collections
    const collectionsToDelete = [
        'applications', 'notifications', 'likedDiscussions', 'dislikedDiscussions',
        'likedDiscussionComments', 'dislikedDiscussionComments',
        'likedNestedDiscussionComments', 'dislikedNestedDiscussionComments',
        'likedFileComments', 'dislikedFileComments',
        'likedNestedFileComments', 'dislikedNestedFileComments'
    ];

    for (const collectionName of collectionsToDelete) {
        const querySnapshotUID = await db.collection(collectionName).where('uid', '==', userID).get();
        if (!querySnapshotUID.empty) {
            querySnapshotUID.docs.forEach(doc => doc.ref.delete());
        }

        const querySnapshotUserID = await db.collection(collectionName).where('userID', '==', userID).get();
        if (!querySnapshotUserID.empty) {
            querySnapshotUserID.docs.forEach(doc => doc.ref.delete());
        }
    }

    // Delete project subscriptions document
    const subscriptionDocRef = db.collection('projectSubscriptions').doc(userID);
    const subscriptionDoc = await subscriptionDocRef.get();
    if (subscriptionDoc.exists) {
        const subscribedProjects = subscriptionDoc.data();

        for (const projectID in subscribedProjects) {
            if (subscribedProjects.hasOwnProperty(projectID)) {
                const projectDocRef = db.collection('projects').doc(projectID);
                await projectDocRef.update({
                    'Subscribers': admin.firestore.FieldValue.increment(-1)
                });
            }

        }

        await subscriptionDocRef.delete();
    }

    // Update user data in discussions and fileComments collections and their nested collections
    await updateUserDataForDeletion(userID);

    // Delete the user document in 'users' collection
    const userDocRef = db.collection('users').doc(userID);
    await userDocRef.delete();

    // Delete the user from Firebase Authentication
    try {
        await admin.auth().deleteUser(userID);
        console.log(`Successfully deleted user from Authentication: ${userID}`);
    } catch (error) {
        console.error(`Error deleting user from Authentication: ${error}`);
        throw new functions.https.HttpsError('internal', `Failed to delete user from Authentication: ${error}`);
    }

    // Function to update user data in documents and nested collections
    async function updateUserDataForDeletion(userID) {
        const collectionsToCheck = ['discussions', 'fileComments'];
        for (const collectionName of collectionsToCheck) {
            const collection = db.collection(collectionName);

            // Query for documents with 'uid' matching 'userID'
            const querySnapshotUID = await collection.where('uid', '==', userID).get();
            await updateDocuments(querySnapshotUID);

            // Query for documents with 'userID' matching 'userID'
            const querySnapshotUserID = await collection.where('userID', '==', userID).get();
            await updateDocuments(querySnapshotUserID);
        }
    }

    async function updateDocuments(querySnapshot) {
        for (const doc of querySnapshot.docs) {
            const updates = {};
            if (doc.data().username) {
                updates.username = 'Account Removed';
            }
            if (doc.data().displayName) {
                updates.displayName = 'Account Removed';
            }
            if (doc.data().uid === userID) {
                updates.uid = 'Account Removed';
            }
            if (doc.data().userID === userID) {
                updates.userID = 'Account Removed';
            }
            if (Object.keys(updates).length > 0) {
                await doc.ref.update(updates);
            }

            // Update nested collections
            await updateNestedCollections(doc.ref, userID);
        }
    }

    async function updateNestedCollections(docRef, userID) {
        const subcollections = await docRef.listCollections();
        for (const subcollection of subcollections) {
            const docs = await subcollection.listDocuments();
            for (const doc of docs) {
                const nestedDocSnapshot = await doc.get();
                const updates = {};
                if (nestedDocSnapshot.data().username) {
                    updates.username = 'Account Removed';
                }
                if (nestedDocSnapshot.data().displayName) {
                    updates.displayName = 'Account Removed';
                }
                if (nestedDocSnapshot.data().uid === userID) {
                    updates.uid = 'Account Removed';
                }
                if (nestedDocSnapshot.data().userID === userID) {
                    updates.userID = 'Account Removed';
                }
                if (Object.keys(updates).length > 0) {
                    await doc.update(updates);
                }

                // Recursively update nested collections within nested collections
                await updateNestedCollections(doc, userID);
            }
        }
    }
});