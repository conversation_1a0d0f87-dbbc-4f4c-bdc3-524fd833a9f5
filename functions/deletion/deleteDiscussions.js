const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.deleteDiscussions = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError(
            'permission-denied',
            'User must be authenticated to delete discussions.'
        );
    }

    //const documentId = data.documentId;
    const documentId = data.discussionID;
    const discussionFileUrls = [];

    try {
        const db = admin.firestore();
        const storage = admin.storage();
        const discussionRef = db.collection('discussions').doc(documentId);
        const discussionDoc = await discussionRef.get();

        // Ensure discussion document exists
        if (!discussionDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Discussion document not found');
        }

        // Check if user is project owner
        const projectId = discussionDoc.data().projectID;
        const projectDoc = await db.collection('projects').doc(projectId).get();

        // Check existence of project document
        if (!projectDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Project document not found');
        }

        const projectOwner = projectDoc.data().uid || null;

        // Check user document existence
        const userDoc = await db.collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User document not found');
        }

        const userPlan = userDoc.data()['Account Type'];

        const uid = discussionDoc.data().uid;
        //Verify permission and user plan
        if ((userPlan !== 'BookBranch+')) {
            throw new functions.https.HttpsError(uid !== context.auth.uid ||
                'permission-denied',
                'User does not have permission to delete this discussion.'
            );
        }
        //projectOwner !== context.auth.uid) || uid !== context.auth.uid ||
        const storageUsedDiscussion = discussionDoc.data()['Storage Used'] || 0;

        // Retrieve associated files from 'discussionFiles'
        const filesSnapshot = await db.collection('discussionFiles')
            .where('discussionID', '==', documentId)
            .get();

        filesSnapshot.forEach(doc => {
            const data = doc.data();
            if (data && data.fileUrl) {
                discussionFileUrls.push(data.fileUrl);
            }
        });

        // Retrieve other snapshots
        const likesSnapshot = await db.collection('likedDiscussionComments')
            .where('discussionID', '==', documentId)
            .get();

        const dislikesSnapshot = await db.collection('dislikedDiscussionComments')
            .where('discussionID', '==', documentId)
            .get();

        const nestedLikesSnapshot = await db.collection('likedNestedDiscussionComments')
            .where('discussionID', '==', documentId)
            .get();

        const nestedDislikesSnapshot = await db.collection('dislikedNestedDiscussionComments')
            .where('discussionID', '==', documentId)
            .get();

        // Delete associated files if they exist
        if (!filesSnapshot.empty) {
            const bucket = storage.bucket();

            const fileDeletionPromises = discussionFileUrls.map(fileUrl => {
                // Parse file path safely
                const filePathParts = fileUrl.split('/o/');
                if (filePathParts.length < 2) return Promise.resolve();
                const filePath = decodeURIComponent(filePathParts[1].split('?')[0].replace(/%2F/g, '/'));
                return bucket.file(filePath).delete().catch(err => {
                    console.error('Error deleting file:', fileUrl, err);
                    return null;
                });
            });

            await Promise.all(fileDeletionPromises);

            // Delete all file documents
            const fileDocDeletionPromises = filesSnapshot.docs.map(doc => doc.ref.delete());
            await Promise.all(fileDocDeletionPromises);
        }

        // Safely delete documents in other snapshots if they exist
        const deleteDocs = async (snapshot) => {
            if (!snapshot.empty) {
                const deletionPromises = snapshot.docs.map(doc => doc.ref.delete());
                await Promise.all(deletionPromises);
            }
        };

        await Promise.all([
            deleteDocs(likesSnapshot),
            deleteDocs(dislikesSnapshot),
            deleteDocs(nestedLikesSnapshot),
            deleteDocs(nestedDislikesSnapshot)
        ]);

        // Calculate how much storage to actually decrement
        // We need to check each file to see if it's a duplicate
        let actualStorageToDecrement = 0;

        // Process each file to determine if it's a duplicate
        if (filesSnapshot.size > 0) {
            for (const fileDoc of filesSnapshot.docs) {
                const fileData = fileDoc.data();
                const fileSize = Number(fileData.fileSize || 0);
                const fileHash = fileData.fileHash;
                let isDuplicate = false;

                // If we have a file hash, check if this file exists elsewhere
                if (fileHash) {
                    console.log(`Checking if file with hash ${fileHash} exists elsewhere`);

                    // Check if this file URL exists in other documents in the 'discussionFiles' collection
                    const otherDiscussionFilesWithSameHash = await db.collection('discussionFiles')
                        .where('fileHash', '==', fileHash)
                        .where('fileUrl', '!=', fileData.fileUrl)
                        .get();

                    // Check if this file URL exists in the 'data' collection
                    const dataDocsWithSameHash = await db.collection('data')
                        .where('fileHash', '==', fileHash)
                        .get();

                    // If the file exists elsewhere, it's a duplicate
                    isDuplicate = otherDiscussionFilesWithSameHash.size > 0 || dataDocsWithSameHash.size > 0;

                    console.log(`File is ${isDuplicate ? 'a duplicate' : 'not a duplicate'}`);
                } else {
                    // If no hash is found, try to check by file name as a fallback
                    const fileName = fileData.fileName || '';
                    if (fileName) {
                        console.log(`No hash found, checking for duplicates by file name: ${fileName}`);

                        // Check if this file name exists in other documents in the 'discussionFiles' collection
                        const otherDiscussionFilesWithSameName = await db.collection('discussionFiles')
                            .where('fileName', '==', fileName)
                            .where('fileUrl', '!=', fileData.fileUrl)
                            .get();

                        // Check if this file name exists in the 'data' collection
                        const dataDocsWithSameName = await db.collection('data')
                            .where('fileName', '==', fileName)
                            .get();

                        // If a file with the same name exists elsewhere, consider it a potential duplicate
                        isDuplicate = otherDiscussionFilesWithSameName.size > 0 || dataDocsWithSameName.size > 0;

                        console.log(`Based on file name, file is ${isDuplicate ? 'a potential duplicate' : 'not a duplicate'}`);
                    } else {
                        console.log('No file hash or name found, treating as a non-duplicate file');
                    }
                }

                // Only count non-duplicate files towards storage decrement
                if (!isDuplicate) {
                    actualStorageToDecrement += fileSize;
                }
            }
        } else {
            // If there are no files, use the discussion's storage value
            actualStorageToDecrement = storageUsedDiscussion;
        }

        console.log(`Actual storage to decrement: ${actualStorageToDecrement} (out of ${storageUsedDiscussion})`);

        // Update the user's storage usage safely
        const userRef = db.collection('users').doc(uid);
        await userRef.update({
            'Storage Used': admin.firestore.FieldValue.increment(-actualStorageToDecrement)
        });

        // Update the project's storage usage safely
        const projectRef = db.collection('projects').doc(projectId);
        await projectRef.update({
            'Storage Used': admin.firestore.FieldValue.increment(-actualStorageToDecrement)
        });

        // Delete subcollections recursively within 'discussions' before deleting the discussion document
        await deleteSubcollections(db, 'discussions', documentId);

        // Finally, delete the discussion document
        await discussionRef.delete();

        return { result: 'Discussion and related files deleted successfully' };

    } catch (error) {
        console.error('Error in deleting discussions:', error);
        throw new functions.https.HttpsError('internal', 'Failed to delete discussions and related files.', error);
    }
});

async function deleteSubcollections(db, collectionPath, documentId) {
    const documentRef = db.collection(collectionPath).doc(documentId);
    await deleteNestedCollections(documentRef);
}

async function deleteNestedCollections(documentRef) {
    try {
        const subcollections = await documentRef.listCollections();
        for (const collection of subcollections) {
            const docs = await collection.listDocuments();
            const docDeletionPromises = docs.map(doc => deleteNestedCollections(doc));
            await Promise.all(docDeletionPromises);
            const deletionPromises = docs.map(doc => doc.delete());
            await Promise.all(deletionPromises);
        }
    } catch (error) {
        console.error('Error deleting nested collections:', error);
    }
}