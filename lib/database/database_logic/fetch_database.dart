import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FetchDatabase {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<int> getChapterCount(WidgetRef ref) async {
    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(await ref.watch(currentProjectID))
        .collection("chapters")
        .get();

    return snapshot.docs.length;
  }

  Future<int> getProjectCount() async {
    QuerySnapshot snapshot = await db.collection("projects").get();
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    List<QueryDocumentSnapshot<Object?>> uidProjects = [];

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
      if (data['uid'] == userUid) {
        uidProjects.add(doc);
      }
    }
    return uidProjects.length;
  }

  Future<List<String>> getAllChapterTitles(WidgetRef ref) async {
    List<String> chapterTitles = [];

    String projectID = ref.watch(currentProjectID);

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection("chapters")
        .get();

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      if (data.containsKey('title')) {
        String title = data['title'];
        chapterTitles.add(title);
      } else {
        print("Document ${doc.id} does not contain a 'title' field.");
      }
    }
    return chapterTitles;
  }

  Future<List<String>> getDocNames(WidgetRef ref, String sectionType) async {
    List<String> chapterDocs = [];
    String projectID = ref.watch(currentProjectID);

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .get();

    for (var doc in snapshot.docs) {
      // Extract the 'title' field and add it to the list
      chapterDocs.add(doc.id);
    }
    return chapterDocs;
  }

  Future<List<String>> getDocNamesChaptersWidget(String projectID) async {
    List<String> chapterDocs = [];
    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection("chapters")
        .get();

    for (var doc in snapshot.docs) {
      chapterDocs.add(doc.id);
    }

    print(chapterDocs);
    print(projectID);
    return chapterDocs;
  }

  Future<String> getUsername() async {
    String username = "";
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    if (userUid != null && userUid.isNotEmpty) {
      DocumentSnapshot userDoc =
          await db.collection('users').doc(userUid).get();
      Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
      if (userData != null && userData.containsKey('Display Name')) {
        username = userData['Display Name'] as String;
      } else {
        print('The user document does not have a username field or is null.');
      }
    } else {
      print('No user is currently signed in or the UID is null.');
    }

    return username;
  }

  Future<String> getProjectDescription(String projectID) async {
    DocumentSnapshot snapshot =
        await db.collection('projects').doc(projectID).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Description') == true) {
      return data!['Description'] as String;
    } else {
      return 'No description available';
    }
  }

  Future<String> getProjectTitle(String projectID) async {
    String title = "";
    DocumentSnapshot snapshot =
        await db.collection('projects').doc(projectID).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Project Name') == true) {
      title = data!['Project Name'] as String;
    }
    return title;
  }

  Future<String> getProjectVisibility(String projectID) async {
    DocumentSnapshot snapshot =
        await db.collection('projects').doc(projectID).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Visibility') == true) {
      return data!['Visibility'] as String;
    } else {
      return 'No description available';
    }
  }

  Future<int> fetchUploadedBytesByUser() async {
    String userId = auth.currentUser!.uid;
    DocumentSnapshot userDoc =
        await FirebaseFirestore.instance.collection('users').doc(userId).get();
    Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
    if (userData != null && userData.containsKey('Storage Used')) {
      return userData['Storage Used'] as int;
    } else {
      return 0;
    }
  }

  Future<int> fetchUploadedBytesByProject(WidgetRef ref) async {
    DocumentSnapshot userDoc = await FirebaseFirestore.instance
        .collection('projects')
        .doc(ref.watch(currentProjectID))
        .get();
    Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
    if (userData != null && userData.containsKey('Storage Used')) {
      return userData['Storage Used'] as int;
    } else {
      return 0;
    }
  }

  Future<int> fetchUploadLimitByUser() async {
    String userId = auth.currentUser!.uid;
    DocumentSnapshot userDoc =
        await FirebaseFirestore.instance.collection('users').doc(userId).get();
    Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
    if (userData != null && userData.containsKey('Storage Limit')) {
      return userData['Storage Limit'] as int;
    } else {
      return 0;
    }
  }
}
