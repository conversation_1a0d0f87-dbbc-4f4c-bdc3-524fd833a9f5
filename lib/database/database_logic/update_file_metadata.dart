import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';

class UpdateFileMetadata {
  Future<void> updateFileDisplayName(
      String fileUrl, String newDisplayName, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print("UpdateFileMetadata: Searching for file with URL: $fileUrl");
      print("UpdateFileMetadata: URL length: ${fileUrl.length}");
      print(
          "UpdateFileMetadata: URL starts with: ${fileUrl.substring(0, 50)}...");

      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      print("UpdateFileMetadata: Found ${querySnapshot.docs.length} documents");

      // If no documents found, let's try to find any documents and compare URLs
      if (querySnapshot.docs.isEmpty) {
        print(
            "UpdateFileMetadata: No documents found, checking all documents in collection...");
        final allDocs = await dataCollection.limit(5).get();
        for (var doc in allDocs.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final docUrl = data['File URL'] as String?;
          if (docUrl != null) {
            print(
                "UpdateFileMetadata: Found document with URL: ${docUrl.substring(0, 50)}...");
            print("UpdateFileMetadata: URLs match: ${docUrl == fileUrl}");
            if (docUrl.contains(fileUrl.substring(50, 100))) {
              print("UpdateFileMetadata: Partial match found!");
            }
          }
        }
      }

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;
        print(
            "UpdateFileMetadata: Updating document $docId with displayName: $newDisplayName");

        // Update the 'displayName' field for that document
        await dataCollection.doc(docId).update({
          'displayName': newDisplayName,
        });
        print("UpdateFileMetadata: Document updated successfully.");
        ref.refresh(fileMetadataProvider);
        ref.refresh(fileMetadataProviderEPublishing);
      } else {
        print(
            "UpdateFileMetadata: No matching document found for URL: $fileUrl");
      }
    } catch (e) {
      print("UpdateFileMetadata: Error updating document: $e");
      rethrow;
    }
  }

  Future<void> updateFileDescription(
      String fileUrl, String newDescription, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'displayName' field for that document
        await dataCollection.doc(docId).update({
          'fileDescription': newDescription,
        });
        print("Document updated successfully.");
        ref.refresh(fileMetadataProvider);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> updateFileDescriptionEPublishing(
      String fileUrl, String newDescription, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'displayName' field for that document
        await dataCollection.doc(docId).update({
          'fileDescription': newDescription,
        });
        print("Document updated successfully.");
        ref.refresh(fileMetadataProviderEPublishing);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> uploadFileTagToFirestore(String fileUrl, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print("UpdateFileMetadata: Adding tag '$tag' to file with URL: $fileUrl");
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      print(
          "UpdateFileMetadata: Found ${querySnapshot.docs.length} documents for tag addition");

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;
        print("UpdateFileMetadata: Adding tag '$tag' to document $docId");

        // Update the 'tags' field for that document
        await dataCollection.doc(docId).update({
          'tags': FieldValue.arrayUnion([tag]),
        });
        print("UpdateFileMetadata: Tag added successfully.");
      } else {
        print(
            "UpdateFileMetadata: No matching document found for URL: $fileUrl");
        throw Exception("No document found with the specified file URL");
      }
    } catch (e) {
      print("UpdateFileMetadata: Error adding tag: $e");
      rethrow;
    }
  }

  Future<void> removeFileTagFromFirestore(String fileUrl, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print(
          "UpdateFileMetadata: Removing tag '$tag' from file with URL: $fileUrl");
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      print(
          "UpdateFileMetadata: Found ${querySnapshot.docs.length} documents for tag removal");

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;
        print("UpdateFileMetadata: Removing tag '$tag' from document $docId");

        // Update the 'tags' field for that document
        await dataCollection.doc(docId).update({
          'tags': FieldValue.arrayRemove([tag]),
        });
        print("UpdateFileMetadata: Tag removed successfully.");
      } else {
        print(
            "UpdateFileMetadata: No matching document found for URL: $fileUrl");
        throw Exception("No document found with the specified file URL");
      }
    } catch (e) {
      print("UpdateFileMetadata: Error removing tag: $e");
      rethrow;
    }
  }

  // New methods that use document ID directly for better performance and reliability
  Future<void> updateFileDisplayNameById(
      String documentId, String newDisplayName, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print(
          "UpdateFileMetadata: Updating document $documentId with displayName: $newDisplayName");

      // Update the 'displayName' field for that document
      await dataCollection.doc(documentId).update({
        'displayName': newDisplayName,
      });

      print("UpdateFileMetadata: Document updated successfully.");
      ref.refresh(fileMetadataProvider);
      ref.refresh(fileMetadataProviderEPublishing);
    } catch (e) {
      print("UpdateFileMetadata: Error updating document: $e");
      rethrow;
    }
  }

  Future<void> updateFileDescriptionById(
      String documentId, String newDescription, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print(
          "UpdateFileMetadata: Updating document $documentId with description: $newDescription");

      // Update the 'fileDescription' field for that document
      await dataCollection.doc(documentId).update({
        'fileDescription': newDescription,
      });

      print("UpdateFileMetadata: Document updated successfully.");
      ref.refresh(fileMetadataProvider);
      ref.refresh(fileMetadataProviderEPublishing);
    } catch (e) {
      print("UpdateFileMetadata: Error updating document: $e");
      rethrow;
    }
  }

  Future<void> uploadFileTagToFirestoreById(
      String documentId, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print("UpdateFileMetadata: Adding tag '$tag' to document $documentId");

      // Update the 'tags' field for that document
      await dataCollection.doc(documentId).update({
        'tags': FieldValue.arrayUnion([tag]),
      });

      print("UpdateFileMetadata: Tag added successfully.");
    } catch (e) {
      print("UpdateFileMetadata: Error adding tag: $e");
      rethrow;
    }
  }

  Future<void> removeFileTagFromFirestoreById(
      String documentId, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      print(
          "UpdateFileMetadata: Removing tag '$tag' from document $documentId");

      // Update the 'tags' field for that document
      await dataCollection.doc(documentId).update({
        'tags': FieldValue.arrayRemove([tag]),
      });

      print("UpdateFileMetadata: Tag removed successfully.");
    } catch (e) {
      print("UpdateFileMetadata: Error removing tag: $e");
      rethrow;
    }
  }
}
