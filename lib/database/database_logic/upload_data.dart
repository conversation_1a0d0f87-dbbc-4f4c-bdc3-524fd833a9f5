import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:flutter/foundation.dart'; // For debugPrint
import 'dart:convert'; // For base64 encoding
import 'package:crypto/crypto.dart'; // For generating file hash

class UploadData {
  final FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  // Generate a hash for a file to use for duplicate detection
  String generateFileHash(Uint8List bytes) {
    // Use SHA-256 for a strong hash with low collision probability
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Check if a file with the same hash already exists in the database
  Future<Map<String, String?>> checkForDuplicateFile(String fileHash) async {
    try {
      // Query the 'data' collection for documents with the same file hash
      final QuerySnapshot querySnapshot = await db
          .collection('data')
          .where('fileHash', isEqualTo: fileHash)
          .limit(1)
          .get();

      // If a document with the same hash exists, return its URL and path
      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        final data = doc.data() as Map<String, dynamic>;
        return {
          'fileUrl': data['File URL'],
          'filePath': data['filePath'],
        };
      }

      // Also check the discussionFiles collection
      final QuerySnapshot discussionFilesSnapshot = await db
          .collection('discussionFiles')
          .where('fileHash', isEqualTo: fileHash)
          .limit(1)
          .get();

      if (discussionFilesSnapshot.docs.isNotEmpty) {
        final doc = discussionFilesSnapshot.docs.first;
        final data = doc.data() as Map<String, dynamic>;
        return {
          'fileUrl': data['fileUrl'],
          'filePath': data['filePath'] ?? '',
        };
      }

      // No duplicate found
      return {'fileUrl': null, 'filePath': null};
    } catch (e) {
      debugPrint('Error checking for duplicate file: $e');
      return {'fileUrl': null, 'filePath': null};
    }
  }

  // Use this method to pick a single file and ensure file data is loaded
  Future<PlatformFile?> pickFile() async {
    try {
      debugPrint('Attempting to pick a file with data...');
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        withData: true,
        type: FileType.custom,
        allowedExtensions: [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'pdf',
          'mp4',
          'mov',
          'avi'
        ],
      );

      if (result != null) {
        if (result.files.single.bytes != null) {
          debugPrint(
              'File picked successfully: ${result.files.single.name}, size: ${result.files.single.size}');
          return result.files.single;
        } else {
          debugPrint(
              'File picked but bytes are null: ${result.files.single.name}');

          // Try to read the file from path if bytes are null (for mobile platforms)
          if (result.files.single.path != null) {
            debugPrint(
                'Attempting to read file from path: ${result.files.single.path}');
            try {
              File file = File(result.files.single.path!);
              Uint8List bytes = await file.readAsBytes();

              // Create a new PlatformFile with the bytes
              PlatformFile fileWithBytes = PlatformFile(
                name: result.files.single.name,
                size: bytes.length,
                bytes: bytes,
                path: result.files.single.path,
                readStream: null,
              );

              debugPrint(
                  'Successfully read file from path, size: ${bytes.length}');
              return fileWithBytes;
            } catch (e) {
              debugPrint('Error reading file from path: $e');
            }
          }
          return null;
        }
      } else {
        debugPrint('No file picked.');
        return null;
      }
    } catch (e) {
      debugPrint('Error picking file: $e');
      return null;
    }
  }

  // Use this method to pick multiple files and ensure file data is loaded
  Future<List<PlatformFile>> pickMultipleFiles() async {
    try {
      debugPrint('Attempting to pick multiple files with data...');
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        withData: true,
        type: FileType.custom,
        allowedExtensions: [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'pdf',
          'mp4',
          'mov',
          'avi'
        ],
      );

      if (result != null) {
        debugPrint('Picked ${result.files.length} files');

        // Process each file to ensure it has bytes
        List<PlatformFile> processedFiles = [];

        for (var file in result.files) {
          if (file.bytes != null) {
            debugPrint('File has bytes: ${file.name}, size: ${file.size}');
            processedFiles.add(file);
          } else if (file.path != null) {
            debugPrint(
                'File missing bytes, attempting to read from path: ${file.path}');
            try {
              File fileObj = File(file.path!);
              Uint8List bytes = await fileObj.readAsBytes();

              // Create a new PlatformFile with the bytes
              PlatformFile fileWithBytes = PlatformFile(
                name: file.name,
                size: bytes.length,
                bytes: bytes,
                path: file.path,
                readStream: null,
              );

              debugPrint(
                  'Successfully read file from path: ${file.name}, size: ${bytes.length}');
              processedFiles.add(fileWithBytes);
            } catch (e) {
              debugPrint('Error reading file from path: $e');
            }
          } else {
            debugPrint('File has no bytes and no path: ${file.name}');
          }
        }

        if (processedFiles.isEmpty) {
          debugPrint('No valid files with data found among selected files');
        } else {
          debugPrint(
              'Successfully processed ${processedFiles.length} files with data');
        }

        return processedFiles;
      } else {
        debugPrint('No files picked or picker was canceled');
        return [];
      }
    } catch (e) {
      debugPrint('Error picking multiple files: $e');
      return [];
    }
  }

  Future<Map<String, String?>> uploadFileToFirebaseStorage(
    PlatformFile file,
    WidgetRef ref,
    String directory,
    Function(double) onProgress,
  ) async {
    debugPrint("Starting file upload process");
    try {
      Uint8List? fileBytes;

      // Check if file has bytes
      if (file.bytes == null) {
        debugPrint('File bytes are null, attempting to read from path');

        // Try to read from path if available
        if (file.path != null) {
          try {
            File fileObj = File(file.path!);
            fileBytes = await fileObj.readAsBytes();
            debugPrint(
                'Successfully read ${fileBytes.length} bytes from file path');
          } catch (e) {
            debugPrint('Error reading file from path: $e');
            return {'fileUrl': null, 'filePath': null};
          }
        } else {
          debugPrint('File has no bytes and no path, cannot upload');
          return {'fileUrl': null, 'filePath': null};
        }
      } else {
        fileBytes = file.bytes!;
      }

      // Generate a hash for the file
      final fileHash = generateFileHash(fileBytes);
      debugPrint('Generated file hash: $fileHash');

      // Check if a file with the same hash already exists
      final duplicateCheck = await checkForDuplicateFile(fileHash);
      if (duplicateCheck['fileUrl'] != null) {
        // A duplicate file was found, use its URL and path
        debugPrint(
            'Duplicate file found, using existing URL: ${duplicateCheck['fileUrl']}');

        // Simulate progress to 100% since we're not actually uploading
        onProgress(1.0);

        return duplicateCheck;
      }

      // No duplicate found, proceed with upload
      debugPrint(
          'No duplicate found, uploading file with ${fileBytes.length} bytes');
      final storageRef = FirebaseStorage.instance.ref();
      final filePath = '$directory${file.name}';
      final fileRef = storageRef.child(filePath);

      final uploadTask = fileRef.putData(fileBytes);

      // Listen for progress changes
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress(progress);
      });

      // Wait for the upload to complete
      final snapshot = await uploadTask;
      var _ =
          ref.refresh(fileMetadataProvider); // Use the result to avoid warning

      // Get the download URL
      String? fileUrl = await snapshot.ref.getDownloadURL();
      debugPrint('File uploaded successfully, URL: $fileUrl');

      // Return both the file URL, file path, and file hash
      return {
        'fileUrl': fileUrl,
        'filePath': filePath,
        'fileHash': fileHash,
      };
    } catch (e, stacktrace) {
      debugPrint('Error uploading file to Firebase: $e');
      debugPrint('Stacktrace: $stacktrace');
      return {'fileUrl': null, 'filePath': null};
    }
  }

  //updateFileAndAggregateStorage ===***
  // used when a new discussion is created
  // not when a regular file is uploaded
  Future<Map<String, dynamic>> uploadFileToFirebaseStorageWithMetadata(
    PlatformFile file,
    WidgetRef ref,
    String directory,
    Function(double) onProgress,
    SettableMetadata metadata,
  ) async {
    try {
      Uint8List? fileBytes;

      // Check if file has bytes
      if (file.bytes == null) {
        debugPrint('File bytes are null, attempting to read from path');

        // Try to read from path if available
        if (file.path != null) {
          try {
            File fileObj = File(file.path!);
            fileBytes = await fileObj.readAsBytes();
            debugPrint(
                'Successfully read ${fileBytes.length} bytes from file path');
          } catch (e) {
            debugPrint('Error reading file from path: $e');
            return {'fileUrl': null, 'isDuplicate': false};
          }
        } else {
          debugPrint('File has no bytes and no path, cannot upload');
          return {'fileUrl': null, 'isDuplicate': false};
        }
      } else {
        fileBytes = file.bytes!;
      }

      // Generate a hash for the file
      final fileHash = generateFileHash(fileBytes);
      debugPrint('Generated file hash: $fileHash');

      // Check if a file with the same hash already exists
      final duplicateCheck = await checkForDuplicateFile(fileHash);
      if (duplicateCheck['fileUrl'] != null) {
        // A duplicate file was found, use its URL
        debugPrint(
            'Duplicate file found, using existing URL: ${duplicateCheck['fileUrl']}');

        // Simulate progress to 100% since we're not actually uploading
        onProgress(1.0);

        return {
          'fileUrl': duplicateCheck['fileUrl'],
          'filePath': duplicateCheck['filePath'],
          'fileHash': fileHash,
          'isDuplicate': true
        };
      }

      // No duplicate found, proceed with upload
      debugPrint(
          'No duplicate found, uploading file with ${fileBytes.length} bytes');
      final storageRef = FirebaseStorage.instance.ref();
      final filePath = '$directory${file.name}';
      final fileRef = storageRef.child(filePath);

      // Add fileHash to metadata
      Map<String, String> updatedMetadata =
          Map<String, String>.from(metadata.customMetadata ?? {});
      updatedMetadata['fileHash'] = fileHash;
      SettableMetadata metadataWithHash = SettableMetadata(
        contentType: metadata.contentType,
        customMetadata: updatedMetadata,
        cacheControl: metadata.cacheControl,
        contentDisposition: metadata.contentDisposition,
        contentEncoding: metadata.contentEncoding,
        contentLanguage: metadata.contentLanguage,
      );

      final uploadTask = fileRef.putData(fileBytes, metadataWithHash);

      // Listen for progress changes
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        double progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress(progress);
      });

      // Wait for the upload to complete
      final snapshot = await uploadTask;
      var _ =
          ref.refresh(fileMetadataProvider); // Use the result to avoid warning

      // Get the download URL
      String? fileUrl = await snapshot.ref.getDownloadURL();
      debugPrint('File uploaded successfully with metadata, URL: $fileUrl');

      return {
        'fileUrl': fileUrl,
        'filePath': filePath,
        'fileHash': fileHash,
        'isDuplicate': false
      };
    } catch (e, stacktrace) {
      debugPrint('Error uploading file to Firebase: $e');
      debugPrint('Stacktrace: $stacktrace');
      return {'fileUrl': null, 'isDuplicate': false};
    }
  }

  Future<String?> uploadProjectThumbnailAndGetDownloadUrl(
      XFile image, WidgetRef ref) async {
    // Assuming 'db' is your Firestore instance and 'uploadData' has access to it
    // Also assuming 'currentProjectID' is accessible and gives the current project's ID
    String projectId = ref.watch(currentProjectID);
    var projectDoc = await db.collection("projects").doc(projectId).get();
    var currentThumbnailUrl = projectDoc.data()?['thumbnail'];

    // If the current thumbnail URL is not 'default', delete the file from storage
    if (currentThumbnailUrl != null && currentThumbnailUrl != 'default') {
      try {
        // Create a storage reference from the URL
        FirebaseStorage.instance.refFromURL(currentThumbnailUrl).delete();
        debugPrint("Old thumbnail deleted successfully.");
      } catch (e) {
        debugPrint("Error occurred while deleting old thumbnail: $e");
        // Consider how to handle this error. Perhaps allow the upload to proceed or not based on your app's requirements.
      }
    }

    // Proceed with the new image upload as before
    File file = File(image.path);
    String fileName = image.name;

    try {
      // Create a reference to Firebase storage for the new thumbnail
      final ref =
          FirebaseStorage.instance.ref().child('project_thumbnails/$fileName');

      // Upload the file
      final uploadTask = await ref.putFile(file);

      // Once upload is complete, return the download URL
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      debugPrint('New file uploaded and download URL is $downloadUrl');
      return downloadUrl;
    } catch (e) {
      debugPrint('Error occurred while uploading new file: $e');
      return null;
    }
  }

// uploads metadata to Firebase database.
// This method does not upload the actual file. That is done above.
// I am tinkering with adding file upload size here without adding it to file_metadata.
  Future<bool> uploadData(
    WidgetRef ref, {
    required String projectName,
    required String fileDescription,
    required String fileType,
    required String fileName,
    required String privacy,
    required List<String> tags,
    required String projectID,
    required String currentChapter,
    required String currentSubsection,
    required String userID,
    required String fileURL,
    required String? filePath,
    required int fileSize,
    required String displayName,
    String? fileHash,
  }) async {
    final FirebaseFirestore firestore = FirebaseFirestore.instance;
    final String documentId = firestore.collection('data').doc().id;
    final DocumentReference dataDocumentReference =
        firestore.collection('data').doc(documentId);

    try {
      final Map<String, dynamic> data = {
        'projectName': projectName,
        'fileDescription': fileDescription,
        'fileType': fileType,
        'fileName': fileName,
        'privacy': privacy,
        'tags': tags,
        'projectID': projectID,
        'chapter': currentChapter,
        'subsection': currentSubsection,
        'User ID': auth.currentUser!.uid,
        'File URL': fileURL,
        'filePath': filePath,
        'File Size': fileSize,
        'displayName': displayName,
        'timestamp': FieldValue.serverTimestamp(), // Add timestamp for ordering
        // Add file hash for duplicate detection if available
        if (fileHash != null) 'fileHash': fileHash,
        // Add other data fields as needed
      };

      await dataDocumentReference.set(data);
      debugPrint('Data uploaded successfully with document ID: $documentId');

      // Refresh providers to update UI
      // Ignore the return values since we don't need them
      // ignore: unused_result
      ref.refresh(fileMetadataProvider);
      // ignore: unused_result
      ref.refresh(fileMetadataProviderEPublishing);

      return true;
    } catch (e) {
      debugPrint('Error uploading data: $e');
      return false;
    }
  }
}
