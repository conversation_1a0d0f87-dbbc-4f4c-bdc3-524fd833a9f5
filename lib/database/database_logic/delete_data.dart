import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:firebase_auth/firebase_auth.dart';

class DeleteData extends StateNotifier<List<String>> {
  DeleteData() : super([]);
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  // a lof of this has been moved to cloud functions

// deletes subsection and all corresponding data ---------------
  Future<void> deleteSubSection(WidgetRef ref) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    List<String> urls = [];

    // deletes actual subsection in 'projects' as well as all data beneath it
    try {
      await firestore
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('chapters')
          .doc(ref.watch(currentChapter))
          .collection('subsections')
          .doc(ref.watch(currentSubsection))
          .delete();

      // Delete all metadata within 'data' associated with subsection
      QuerySnapshot querySnapshot = await firestore
          .collection('data')
          .where('subsection', isEqualTo: ref.watch(currentSubsection))
          .get();

      // If the document exists, delete it
      // also get the file urls
      for (var doc in querySnapshot.docs) {
        String fileUrl = doc.get('File URL');
        urls.add(fileUrl);

        await firestore.collection('data').doc(doc.id).delete();
        print('Document deleted successfully.');
      }
    } catch (e) {
      print('Error deleting document: $e');
    }

    // updates state
    ref
        .read(subsectionListProvider.notifier)
        .removeSubsection(ref.watch(currentSubsection));

    // deletes actual file within Firebase Storage
    FirebaseStorage storage = FirebaseStorage.instance;

    for (int i = 0; i < urls.length; i++) {
// Create a reference from the URL
      Reference ref = storage.refFromURL(urls[i]);

      // Delete the file
      await ref.delete();

      print('File deleted successfully.');
    }
  }

  Future<void> deleteEPublishingSubSection(WidgetRef ref) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    List<String> urls = [];

    // deletes actual subsection in 'projects' as well as all data beneath it
    try {
      await firestore
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('epublishing')
          .doc(ref.watch(currentEPublishingChapter))
          .collection('subsections')
          .doc(ref.watch(currentEPublishingSubsection))
          .delete();

      // Delete all metadata within 'data' associated with subsection
      QuerySnapshot querySnapshot = await firestore
          .collection('data')
          .where('subsection',
              isEqualTo: ref.watch(currentEPublishingSubsection))
          .get();

      // If the document exists, delete it
      // also get the file urls
      for (var doc in querySnapshot.docs) {
        String fileUrl = doc.get('File URL');
        urls.add(fileUrl);

        await firestore.collection('data').doc(doc.id).delete();
        print('Document deleted successfully.');
      }
    } catch (e) {
      print('Error deleting document: $e');
    }

    // updates state
    ref
        .read(subsectionListProvider.notifier)
        .removeSubsection(ref.watch(currentSubsection));

    // deletes actual file within Firebase Storage
    FirebaseStorage storage = FirebaseStorage.instance;

    for (int i = 0; i < urls.length; i++) {
// Create a reference from the URL
      Reference ref = storage.refFromURL(urls[i]);

      // Delete the file
      await ref.delete();

      print('File deleted successfully.');
    }
  }

  Future<void> deleteChapter(WidgetRef ref) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    List<String> urls = [];

    // deletes actual chapter in 'projects' as well as all data beneath it
    try {
      CollectionReference subsectionsRef = firestore
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('chapters')
          .doc(ref.watch(currentChapter))
          .collection('subsections');

      // Get all documents in the 'subsections' subcollection
      QuerySnapshot querySnapshot = await subsectionsRef.get();

      // Perform batch deletion
      WriteBatch batch = firestore.batch();
      for (var doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Commit the batch
      await batch.commit();
      print('All subsections deleted successfully.');

      // Now delete the chapter document itself
      await firestore
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('chapters')
          .doc(ref.watch(currentChapter))
          .delete();

      //await chapterRef.delete();
      print('Chapter deleted successfully.');

//------------------------------------------------------------------------
// Delete all metadata within 'data' associated with chapter
      QuerySnapshot querySnapshotData = await firestore
          .collection('data')
          .where('projectID', isEqualTo: ref.watch(currentProjectID))
          .where('chapter', isEqualTo: ref.watch(currentChapter))
          .get();

      // If the document exists, delete it
      // also get the file urls
      for (var doc in querySnapshotData.docs) {
        String fileUrl = doc.get('File URL');
        urls.add(fileUrl);

        await firestore.collection('data').doc(doc.id).delete();
        print('Document deleted successfully.');
      }
    } catch (e) {
      print('Error deleting document: $e');
    }
//--------------------------------------------------------------------------
    // deletes actual file within Firebase Storage
    FirebaseStorage storage = FirebaseStorage.instance;

    for (int i = 0; i < urls.length; i++) {
// Create a reference from the URL
      Reference ref = storage.refFromURL(urls[i]);

      // Delete the file
      await ref.delete();

      print('File deleted successfully.');
    }
    //ref.refresh(fileMetadataProvider);
    final refresh = ref.refresh(fileMetadataProviderChapterLevel);
    refresh;
  }

  Future<void> deleteEPublishingChapter(WidgetRef ref) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    List<String> urls = [];

    // deletes actual chapter in 'projects' as well as all data beneath it
    try {
      CollectionReference subsectionsRef = firestore
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('epublishing')
          .doc(ref.watch(currentEPublishingChapter))
          .collection('subsections');

      // Get all documents in the 'subsections' subcollection
      QuerySnapshot querySnapshot = await subsectionsRef.get();

      // Perform batch deletion
      WriteBatch batch = firestore.batch();
      for (var doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Commit the batch
      await batch.commit();
      print('All subsections deleted successfully.');

      // Now delete the chapter document itself
      await firestore
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('epublishing')
          .doc(ref.watch(currentEPublishingChapter))
          .delete();

      //await chapterRef.delete();
      print('Chapter deleted successfully.');

//------------------------------------------------------------------------
// Delete all metadata within 'data' associated with chapter
      QuerySnapshot querySnapshotData = await firestore
          .collection('data')
          .where('projectID', isEqualTo: ref.watch(currentProjectID))
          .where('chapter', isEqualTo: ref.watch(currentEPublishingChapter))
          .get();

      // If the document exists, delete it
      // also get the file urls
      for (var doc in querySnapshotData.docs) {
        String fileUrl = doc.get('File URL');
        urls.add(fileUrl);

        await firestore.collection('data').doc(doc.id).delete();
        print('Document deleted successfully.');
      }
    } catch (e) {
      print('Error deleting document: $e');
    }
//--------------------------------------------------------------------------
    // deletes actual file within Firebase Storage
    FirebaseStorage storage = FirebaseStorage.instance;

    for (int i = 0; i < urls.length; i++) {
// Create a reference from the URL
      Reference ref = storage.refFromURL(urls[i]);

      // Delete the file
      await ref.delete();

      print('File deleted successfully.');
    }
    //ref.refresh(fileMetadataProvider);
    final refresh = ref.refresh(fileMetadataProviderEPublishingChapterLevel);
    refresh;
  }
}
