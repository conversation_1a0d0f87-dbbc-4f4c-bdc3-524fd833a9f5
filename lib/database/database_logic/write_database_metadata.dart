import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/projects_create_logic.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/dartmain/dartMain_logic/on_login.dart';

// this class DOES NOT deal with file metadata
class WriteDatabase {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;
  ProjectsCreateLogic obj = ProjectsCreateLogic();
  FetchDatabase fetch = FetchDatabase();
  OnLogin onLogin = OnLogin();

  Future<void> setLayoutType(WidgetRef ref) async {
    //String projectID = ref.watch(currentProjectID);
    String layoutType;

    String projectID = ref.watch(currentProjectID);

    // Fetch the collection
    DocumentSnapshot snapshot =
        await db.collection("projects").doc(projectID).get();

    layoutType = snapshot.get('Layout') as String;

    ref.read(currentProjectLayout.notifier).state = layoutType;
  }

  Future<void> editProjectLayout(String layout, WidgetRef ref) async {
    await db
        .collection("projects")
        .doc(await ref.watch(currentProjectID))
        .update({'Layout': layout});
  }

  void addNewChapter(WidgetRef ref, String sectionType, String title) async {
    List<String> docNames = [];
    String projectID = ref.watch(currentProjectID);

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .get();

    for (var doc in snapshot.docs) {
      docNames.add(doc.id);
    }

    // Check if there are any documents in the list
    if (docNames.isNotEmpty) {
      // Retrieve the last document's name
      String lastDocName = docNames.last;

      // Split the last document's name to isolate the number
      var parts = lastDocName.split('_');
      if (parts.length > 1 && int.tryParse(parts.last) != null) {
        int lastNumber = int.parse(parts.last);
        String newDocName =
            '${parts.sublist(0, parts.length - 1).join('_')}_${lastNumber + 1}';
        docNames.add(newDocName);
      }
      // just added
    } else {
      docNames.add("chapter_1");
    }

    // adds the chapter itself
    await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .doc(docNames.last)
        .set({'title': title});
// automatically adds 1 default subsection to each new chapter
    await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .doc(docNames.last)
        .collection("subsections")
        .doc("subsection_1")
        .set({'title': "Subsection 1"});

    // refreshTwo refreshes metadata otherwise there is an index out of bounds error
    final refresh = ref.refresh(fileMetadataProviderChapterLevel);
    final refreshTwo = ref.refresh(titlesProvider);
    refresh;
    refreshTwo;
  }

  void addNewEPublishingChapter(
      WidgetRef ref, String sectionType, String title) async {
    List<String> docNames = [];
    String projectID = ref.watch(currentProjectID);

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .get();

    for (var doc in snapshot.docs) {
      docNames.add(doc.id);
    }

    // Check if there are any documents in the list
    if (docNames.isNotEmpty) {
      // Retrieve the last document's name
      String lastDocName = docNames.last;

      // Split the last document's name to isolate the number
      var parts = lastDocName.split('_');
      if (parts.length > 1 && int.tryParse(parts.last) != null) {
        int lastNumber = int.parse(parts.last);
        String newDocName =
            '${parts.sublist(0, parts.length - 1).join('_')}_${lastNumber + 1}';
        docNames.add(newDocName);
      }
    } else {
      docNames.add("epchapter_1");
    }

    // adds the chapter itself
    await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .doc(docNames.last)
        .set({'title': title});
// automatically adds 1 default subsection to each new chapter
    await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .doc(docNames.last)
        .collection("subsections")
        .doc("subsection_1")
        .set({'title': "Subsection 1"});

    // refreshTwo refreshes metadata otherwise there is an index out of bounds error
    final refresh = ref.refresh(fileMetadataProviderEPublishingChapterLevel);
    final refreshTwo = ref.refresh(titlesProviderEPublishing);
    refresh;
    refreshTwo;
  }

  void changeChapterTitle(String newTitle, WidgetRef ref) async {
    String projectID = ref.watch(currentProjectID);
    String chapter = ref.watch(currentChapter);

    if (projectID.isNotEmpty && chapter.isNotEmpty) {
      DocumentReference subsectionRef = db
          .collection("projects")
          .doc(projectID)
          .collection("chapters")
          .doc(chapter);

      await subsectionRef.update({'title': newTitle});
      print('Chapter title changed to $newTitle');
      final refresh = ref.refresh(fileMetadataProviderChapterLevel);
      refresh;
    } else {
      print('One of the required document path parameters is empty');
    }
  }

  void changeEPublishingChapterTitle(String newTitle, WidgetRef ref) async {
    String projectID = ref.watch(currentProjectID);
    String chapter = ref.watch(currentEPublishingChapter);

    if (projectID.isNotEmpty && chapter.isNotEmpty) {
      DocumentReference subsectionRef = db
          .collection("projects")
          .doc(projectID)
          .collection("epublishing")
          .doc(chapter);

      await subsectionRef.update({'title': newTitle});
      print('Chapter title changed to $newTitle');
      final refresh = ref.refresh(fileMetadataProviderEPublishingChapterLevel);
      refresh;
    } else {
      print('One of the required document path parameters is empty');
    }
  }

  Future<bool> updateDisplayName(String name, WidgetRef ref) async {
    // Check if the display name already exists
    QuerySnapshot userSnapshot = await db
        .collection('users')
        .where('Display Name', isEqualTo: name)
        .get();

    if (userSnapshot.docs.isNotEmpty) {
      // Display name already exists, handle accordingly
      print('Display name already exists.');
      // You can return here or show an error message to the user
      return false;
    }

    // Update the display name in the users collection
    await db
        .collection('users')
        .doc(auth.currentUser!.uid)
        .update({"Display Name": name});

    // Fetch all projects with the current display name
    QuerySnapshot querySnapshot = await db
        .collection('projects')
        .where('Display Name', isEqualTo: auth.currentUser!.displayName)
        .get();

    // Update each project document
    for (QueryDocumentSnapshot doc in querySnapshot.docs) {
      await db
          .collection('projects')
          .doc(doc.id)
          .update({"Display Name": name});
    }

    // Update the display name in the FirebaseAuth user profile
    await auth.currentUser!.updateDisplayName(name);

    ref.read(currentUserFirstLetterProvider.notifier).state =
        await onLogin.chopUsername();

    return true;
  }

  void updateProjectDescription(String description, WidgetRef ref) async {
    await db
        .collection('projects')
        .doc(ref.watch(currentProjectID))
        .update({"Description": description});
  }

  void changeProjectVisiblity(WidgetRef ref, String visibility) async {
    String projectID = ref.watch(currentProjectID);
    //String visibility = ref.watch(projectVisiblityProvider);

    await db
        .collection('projects')
        .doc(projectID)
        .update({'Visibility': visibility});
  }

  Future<void> updateProjectVisibility(
      String projectID, String visibility) async {
    await db
        .collection('projects')
        .doc(projectID)
        .update({'Visibility': visibility});
  }

  Future<void> updateUserNotifications(bool status) async {
    await db
        .collection('users')
        .doc(auth.currentUser!.uid)
        .update({'Notifications': status});
  }
}
