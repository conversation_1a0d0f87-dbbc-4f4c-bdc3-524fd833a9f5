import 'package:cloud_firestore/cloud_firestore.dart';

class FileMetadata {
  final String fileDescription;
  final String fileName;
  final String fileType;
  final String privacy;
  final String projectDescription;
  final String projectID;
  final String projectName;
  final String tags;
  final String displayName;
  final String fileURL;
  final String? filePath;
  final String? fileHash;
  final String? uid;
  final Timestamp? timestamp;

  FileMetadata(
      {required this.fileDescription,
      required this.fileName,
      required this.fileType,
      required this.privacy,
      required this.projectDescription,
      required this.projectID,
      required this.projectName,
      required this.tags,
      required this.displayName,
      required this.fileURL,
      this.filePath,
      this.fileHash,
      this.uid,
      this.timestamp});

  // Factory constructor to create a FileMetadata instance from a Firestore document.
  factory FileMetadata.fromFirestore(DocumentSnapshot doc) {
    Map data = doc.data() as Map;
    return FileMetadata(
        fileDescription: data['fileDescription'] ?? '',
        fileName: data['fileName'] ?? '',
        fileType: data['fileType'] ?? '',
        privacy: data['privacy'] ?? '',
        projectDescription: data['projectDescription'] ?? '',
        projectID: data['projectID'] ?? '',
        projectName: data['projectName'] ?? '',
        tags: data['tags'] ?? '',
        displayName: data['displayName'] ?? '',
        fileURL: data['File URL'] ?? '',
        filePath: data['filePath'],
        fileHash: data['fileHash'],
        uid: data['User ID'],
        timestamp: data['timestamp']);
  }

  static Future<List<FileMetadata>> fetchFileMetadataChapterLevel(
      String currentChapter) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('chapter', isEqualTo: currentChapter)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

  static Future<List<FileMetadata>> fetchFileMetadata(String currentChapter,
      String currentSubsection, String currentProjectID) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('chapter', isEqualTo: currentChapter)
        .where('subsection', isEqualTo: currentSubsection)
        .where('projectID', isEqualTo: currentProjectID)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

// more specific
  static Future<List<FileMetadata>> fetchChosenFileMetadata(
      String chosenFileURL) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('File URL', isEqualTo: chosenFileURL)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

  Future<void> printFileMetadata(String chosenFileURL) async {
    try {
      List<FileMetadata> metadataList =
          await FileMetadata.fetchChosenFileMetadata(chosenFileURL);
      for (var metadata in metadataList) {
        print('File Description: ${metadata.fileDescription}');
        print('File Name: ${metadata.fileName}');
        print('File Type: ${metadata.fileType}');
        // ... Print other properties as needed ...
      }
    } catch (e) {
      print('Error fetching file metadata: $e');
    }
  }

  // Method to get file description by URL
  static Future<String> getFileDescriptionByUrl(String fileUrl) async {
    try {
      List<FileMetadata> metadataList =
          await FileMetadata.fetchChosenFileMetadata(fileUrl);
      if (metadataList.isNotEmpty) {
        // Assuming there's only one file with this URL or you're interested in the first one
        return metadataList.first.fileDescription;
      } else {
        return 'No description found'; // Handle case where no file matches the URL
      }
    } catch (e) {
      print('Error fetching file metadata: $e');
      return 'Error fetching file description';
    }
  }

  // Method to get file metadata by URL
  static Future<FileMetadata?> fetchSingleFileMetadataByUrl(
      String fileUrl) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('File URL', isEqualTo: fileUrl)
        .get();

    // Check if at least one document was found
    if (querySnapshot.docs.isNotEmpty) {
      // Return the first document's data as a FileMetadata object
      return FileMetadata.fromFirestore(querySnapshot.docs.first);
    } else {
      // Return null if no document matches the URL
      return null;
    }
  }
}
