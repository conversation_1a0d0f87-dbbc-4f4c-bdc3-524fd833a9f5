import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/main.dart';

class WriteDatabaseSubsections extends StateNotifier<List<String>> {
  WriteDatabaseSubsections() : super([]);

  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  FetchDatabase fetch = FetchDatabase();

  void addSubsection(String docName) {
    state = [...state, docName]; // Add the new docName to the current state
  }

  void removeSubsection(String title) {
    state = state.where((subsection) => subsection != title).toList();
  }

  void addNewSubsection(WidgetRef ref, String sectionType, String title) async {
    String projectID = ref.watch(currentProjectID);
    List<String> docNames = [];

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .doc(ref.watch(currentChapter))
        .collection("subsections")
        .get();

    for (var doc in snapshot.docs) {
      docNames.add(doc.id);
    }

    String newDocName = "";
    // Check if there are any documents in the list
    if (docNames.isNotEmpty) {
      // Retrieve the last document's name
      String lastDocName = docNames.last;

      // Split the last document's name to isolate the number
      var parts = lastDocName.split('_');
      if (parts.length > 1 && int.tryParse(parts.last) != null) {
        int lastNumber = int.parse(parts.last);
        newDocName =
            '${parts.sublist(0, parts.length - 1).join('_')}_${lastNumber + 1}';
        docNames.add(newDocName);
      }
    } else {
      newDocName =
          "subsection_1"; // Assuming a default name for the first document
    }

    await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .doc(ref.watch(currentChapter))
        .collection("subsections")
        .doc(newDocName)
        .set({'title': title});

    ref.read(subsectionListProvider.notifier).addSubsection(newDocName);
  }

  void addNewEPublishingSubsection(WidgetRef ref, String title) async {
    String projectID = ref.watch(currentProjectID);
    List<String> docNames = [];

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection('epublishing')
        .doc(ref.watch(currentEPublishingChapter))
        .collection("subsections")
        .get();

    for (var doc in snapshot.docs) {
      docNames.add(doc.id);
    }

    String newDocName = "";
    // Check if there are any documents in the list
    if (docNames.isNotEmpty) {
      // Retrieve the last document's name
      String lastDocName = docNames.last;

      // Split the last document's name to isolate the number
      var parts = lastDocName.split('_');
      if (parts.length > 1 && int.tryParse(parts.last) != null) {
        int lastNumber = int.parse(parts.last);
        newDocName =
            '${parts.sublist(0, parts.length - 1).join('_')}_${lastNumber + 1}';
        docNames.add(newDocName);
      }
    } else {
      newDocName =
          "subsection_1"; // Assuming a default name for the first document
    }

    await db
        .collection("projects")
        .doc(projectID)
        .collection('epublishing')
        .doc(ref.watch(currentEPublishingChapter))
        .collection("subsections")
        .doc(newDocName)
        .set({'title': title});

    ref.read(subsectionListProvider.notifier).addSubsection(newDocName);
  }

  void changeSubsectionTitle(String newTitle, WidgetRef ref) async {
    String projectID = ref.watch(currentProjectID);
    String chapter = ref.watch(currentChapter);
    String subsection = ref.watch(currentSubsection);

    if (projectID.isNotEmpty && chapter.isNotEmpty && subsection.isNotEmpty) {
      DocumentReference subsectionRef = db
          .collection("projects")
          .doc(projectID)
          .collection("chapters")
          .doc(chapter)
          .collection("subsections")
          .doc(subsection);

      await subsectionRef.update({'title': newTitle});
      print('Subsection title changed to $newTitle');
      final refresh = ref.refresh(fileMetadataProvider);
      refresh;
    } else {
      print('One of the required document path parameters is empty');
    }
  }

  void changeEPublishingSubsectionTitle(String newTitle, WidgetRef ref) async {
    String projectID = ref.watch(currentProjectID);
    String chapter = ref.watch(currentEPublishingChapter);
    String subsection = ref.watch(currentEPublishingSubsection);

    if (projectID.isNotEmpty && chapter.isNotEmpty && subsection.isNotEmpty) {
      DocumentReference subsectionRef = db
          .collection("projects")
          .doc(projectID)
          .collection("epublishing")
          .doc(chapter)
          .collection("subsections")
          .doc(subsection);

      await subsectionRef.update({'title': newTitle});
      print('Subsection title changed to $newTitle');
      final refresh = ref.refresh(fileMetadataProviderEPublishing);
      refresh;
    } else {
      print('One of the required document path parameters is empty');
    }
  }
}
