import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:file_picker/file_picker.dart';
import 'package:web_app/database/database_logic/upload_data.dart';
import 'package:web_app/widgets/universal_widgets.dart';

class DiscussionsFileSelector {
  final UploadData dataObj = UploadData();
  final FirebaseAuth auth = FirebaseAuth.instance;
  final UniversalWidgets universals = UniversalWidgets();

  void createDiscussionPopup(
      BuildContext context,
      List<String> messages,
      List<TextEditingController> controllers,
      List<String> attachedFiles,
      List<PlatformFile> platformFiles,
      WidgetRef ref,
      VoidCallback onPressedAction) {
    assert(messages.length == controllers.length,
        'There must be an equal number of messages and text controllers.');

    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text(
                'New Discussion',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                  color: Colors.black87,
                ),
              ),
              titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
              contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    for (int i = 0; i < messages.length; i++) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Text(
                          messages[i],
                          style: const TextStyle(
                            fontSize: 16.0,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      TextField(
                        controller: controllers[i],
                        decoration: InputDecoration(
                          labelText: "Enter ${messages[i]}",
                          labelStyle: const TextStyle(color: Colors.grey),
                          enabledBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey),
                          ),
                          focusedBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: bookBranchGreen),
                          ),
                        ),
                        style: const TextStyle(fontSize: 16),
                        maxLines: 1,
                      ),
                    ],
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: Text(
                        'Attached Files',
                        style: TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    // Display already attached files
                    if (attachedFiles.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: attachedFiles
                              .map((file) => Padding(
                                    padding:
                                        const EdgeInsets.symmetric(vertical: 4),
                                    child: Text(
                                      file,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ))
                              .toList(),
                        ),
                      ),
                    // File attachment button with styling similar to universal widgets
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(6.0),
                      ),
                      child: InkWell(
                        onTap: () async {
                          PlatformFile? pickedFile = await pickFile();
                          if (pickedFile != null) {
                            setState(() {
                              attachedFiles.add(pickedFile.name);
                              platformFiles.add(pickedFile);
                            });
                          }
                        },
                        child: const Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 14),
                          child: Row(
                            children: [
                              Icon(Icons.attach_file, color: Colors.grey),
                              SizedBox(width: 16),
                              Text(
                                'Attach File',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // Dismiss the keyboard using FocusManager
                          FocusManager.instance.primaryFocus?.unfocus();

                          onPressedAction();
                          for (var controller in controllers) {
                            controller.clear();
                          }
                          Navigator.of(dialogContext).pop(); // Close the dialog
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: bookBranchGreen,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.0),
                          ),
                          elevation: 1,
                        ),
                        child: const Text(
                          'OK',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              shape: RoundedRectangleBorder(
                side: const BorderSide(color: bookBranchGreen, width: 1.5),
                borderRadius: BorderRadius.circular(8.0),
              ),
              backgroundColor: Colors.white,
              elevation: 3,
            );
          },
        );
      },
    ).then((_) {
      // Called when the dialog is dismissed (including tapping outside)
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }

// Adjusted pickFile method:
  Future<PlatformFile?> pickFile() async {
    FilePickerResult? result =
        await FilePicker.platform.pickFiles(withData: true);
    if (result != null && result.files.single.bytes != null) {
      return result.files.single;
    } else {
      // File was not picked or is missing data
      return null;
    }
  }
}
