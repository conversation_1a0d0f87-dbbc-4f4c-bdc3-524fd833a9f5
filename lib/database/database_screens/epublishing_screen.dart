import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/upload_data.dart';
import 'package:web_app/dartMain/dartMain_logic/create_new_user.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/database/database_widgets/chapters_widget.dart';
import 'package:web_app/database/database_widgets/elevatedButtons.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/database/database_widgets/epublishing_widget.dart';

import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class EPublishingScreen extends ConsumerWidget {
  static const String routeName = '/databaseMain';

  final UploadData dataObj = UploadData();
  //final CreateAccountScreen authObject = CreateAccountScreen();
  final CreateNewUser firestoreObj = CreateNewUser();
  final WriteDatabase writeDataObj = WriteDatabase();
  final DatabaseChapters databaseChapters = DatabaseChapters();
  final ElevatedButtons elevatedButton = ElevatedButtons();
  final FetchDatabase fetch = FetchDatabase();

  final TextEditingController projectName = TextEditingController();
  final TextEditingController projectDescription = TextEditingController();
  final TextEditingController projectId = TextEditingController();
  final TextEditingController privacy = TextEditingController();
  final TextEditingController tags = TextEditingController();
  final TextEditingController fileName = TextEditingController();
  final TextEditingController fileDescription = TextEditingController();
  final String selectedFileType = ""; // Default file type
  final String selectedPrivacy = "Public"; // Default privacy option

  // This will hold the picked file's name
  String? selectedFileName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //fetch.fetchDocNames(ref, "epublishing");
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Branches',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: EPublishingChapters(),
      floatingActionButton: Stack(
        alignment: Alignment.bottomRight,
        children: [
          Positioned(
            right: 0,
            child: FloatingActionButton(
              heroTag: 'newChapter',
              backgroundColor: globals.bookBranchGreen,
              foregroundColor: Colors.white,
              highlightElevation: 50,
              tooltip: "New Chapter",
              onPressed: () {
                elevatedButton.createNewEPublishingChapterPopUp(context, ref);
              },
              child: const Icon(Icons.add),
            ),
          ),
        ],
      ),
    );
  }
}
