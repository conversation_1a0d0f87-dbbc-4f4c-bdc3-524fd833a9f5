import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FileDetailsScreen extends ConsumerWidget {
  static const String routeName = '/databaseChapterDetails';
  final String?
      expectedDisplayName; // Expected display name to help identify the correct duplicate
  final String?
      expectedDescription; // Expected description to help identify the correct duplicate
  final Timestamp?
      expectedTimestamp; // Expected timestamp to help identify the correct duplicate

  const FileDetailsScreen({
    this.expectedDisplayName,
    this.expectedDescription,
    this.expectedTimestamp,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WillPopScope(
      onWillPop: () async {
        ref.refresh(fileMetadataProvider);
        return true;
      },
      child: Scaffold(
        body: FileDetailsWidget(
          fileUrl: ref.watch(chosenFileUrl),
          expectedDisplayName: expectedDisplayName,
          expectedDescription: expectedDescription,
          expectedTimestamp: expectedTimestamp,
        ),
      ),
    );
  }
}
