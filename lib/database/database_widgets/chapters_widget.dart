import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/database/database_screens/subsections.dart';
import 'package:web_app/database/database_logic/delete_data.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';

class DatabaseChapters extends ConsumerStatefulWidget {
  final WriteDatabase databaseObj = WriteDatabase();
  final FetchDatabase fetch = FetchDatabase();

  @override
  _DatabaseChaptersState createState() => _DatabaseChaptersState();
}

class _DatabaseChaptersState extends ConsumerState<DatabaseChapters> {
  List<String>? titlesData;
  final DeleteData deleteData = DeleteData();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final titlesAsyncValue = ref.watch(titlesProvider);
    titlesAsyncValue.when(
      data: (data) {
        setState(() => titlesData = data);
      },
      loading: () {},
      error: (e, st) {},
    );

    final fileMetadata = ref.watch(fileMetadataProviderChapterLevel);
    fileMetadata;

    return FutureBuilder<List<String>>(
      future: widget.fetch.fetchAllChapterTitles(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasError) {
            return Text('Error: ${snapshot.error}');
          } else if (snapshot.hasData) {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: const Color.fromARGB(
                          255, 44, 148, 44), // Green outline
                      width: 1.5, // Slightly thicker outline
                    ),
                  ),
                  child: InkWell(
                    onTap: () {
                      if (titlesData != null && titlesData!.isNotEmpty) {
                        ref.read(currentChapter.notifier).state =
                            titlesData![index];
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return Subsections();
                        }));
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.menu_book_outlined,
                                  color: Color.fromARGB(255, 44, 148, 44),
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Flexible(
                                  child: Text(
                                    snapshot.data![index],
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black87,
                                      letterSpacing: 0.2,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(
                                  Icons.delete_outline,
                                  color: Color.fromARGB(255, 44, 148, 44),
                                ),
                                onPressed: () {
                                  ref.read(currentChapter.notifier).state =
                                      titlesData![index];
                                  deleteData.deleteChapter(ref);
                                },
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.edit_outlined,
                                  color: Color.fromARGB(255, 44, 148, 44),
                                ),
                                onPressed: () {
                                  ref.read(currentChapter.notifier).state =
                                      titlesData![index];
                                  changeChapterTitlePopup(context, ref);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          } else {
            // Handle the case when there's no error and no data
            return const Center(child: Text('No data available'));
          }
        } else {
          // Handle the case when data is still loading
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  void changeChapterTitlePopup(BuildContext context, WidgetRef ref) {
    TextEditingController titleController = TextEditingController();
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text(
            'Change Title',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: TextField(
            controller: titleController,
            decoration: const InputDecoration(
              hintText: "Enter new title",
              hintStyle: TextStyle(color: Colors.grey),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: bookBranchGreen),
              ),
            ),
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey,
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: bookBranchGreen,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero,
                ),
              ),
              onPressed: () {
                String newTitle = titleController.text;
                if (newTitle.isNotEmpty) {
                  widget.databaseObj.changeChapterTitle(newTitle, ref);
                  Navigator.of(context).pop(); // Close the dialog
                } else {
                  // Optionally handle empty title case
                }
              },
              child: const Text('OK'),
            ),
          ],
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
          ),
        );
      },
    );
  }
}
