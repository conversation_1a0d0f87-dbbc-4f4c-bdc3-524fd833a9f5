import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/upload_data.dart';
import 'package:web_app/dartMain/dartMain_logic/create_new_user.dart';
import 'package:file_picker/file_picker.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/database/database_logic/write_database_subsections.dart';
import 'package:web_app/database/database_widgets/chapters_widget.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ElevatedButtons {
  final UploadData dataObj = UploadData();
  //final CreateAccountWidget authObject = CreateAccountWidget();
  final CreateNewUser firestoreObj = CreateNewUser();
  final WriteDatabase writeDataObj = WriteDatabase();
  final DatabaseChapters databaseChapters = DatabaseChapters();
  final WriteDatabaseSubsections writeDataSub = WriteDatabaseSubsections();

  final TextEditingController projectName = TextEditingController();
  final TextEditingController projectDescription = TextEditingController();
  final TextEditingController projectId = TextEditingController();
  final TextEditingController privacy = TextEditingController();
  final TextEditingController tags = TextEditingController();
  final TextEditingController fileName = TextEditingController();
  final TextEditingController fileDescription = TextEditingController();
  final TextEditingController sectionTitle = TextEditingController();
  final TextEditingController displayName = TextEditingController();

  String selectedFileType = "Image"; // Default file type
  String selectedPrivacy = "Public"; // Default privacy option

  // This will hold the picked file's name
  String? selectedFileName;

  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<void> _pickFileMobile(BuildContext context, WidgetRef ref) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'pdf',
        'mp4',
        'mov',
        'avi'
      ],
    );

    if (result != null) {
      PlatformFile file = result.files.first;

      // Update the state with the picked file
      //ref.read(pickedFileProvider.notifier).state = file;

      // Optionally show a snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('File selected: ${file.name}')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No file selected')),
      );
    }
  }

  void createSettingsPopUp(BuildContext context, WidgetRef ref) {
    // Local state that mirrors the global state
    String localLayout = ref.watch(currentProjectLayout);

    showDialog(
      context: context,
      builder: (dialogContext) {
        // Use a StatefulBuilder to rebuild only the dialog content
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: const Text("Project Settings"),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ListTile(
                    title: const Text('Chapters'),
                    leading: Radio<String>(
                      value: 'chapters',
                      groupValue: localLayout,
                      onChanged: (value) {
                        setState(
                            () => localLayout = value!); // Update local state
                      },
                      activeColor: Colors.blue,
                    ),
                  ),
                  ListTile(
                    title: const Text('Freeform'),
                    leading: Radio<String>(
                      value: 'freeform',
                      groupValue: localLayout,
                      onChanged: (value) {
                        setState(
                            () => localLayout = value!); // Update local state
                      },
                      activeColor: Colors.blue,
                    ),
                  ),
                ],
              ),
              actions: <Widget>[
                ElevatedButton(
                  onPressed: () {
                    if (localLayout.isNotEmpty) {
                      ref.read(currentProjectLayout.notifier).state =
                          localLayout;
                      writeDataObj.editProjectLayout(localLayout, ref);

                      //databaseChapters.build(context, ref);
                      Navigator.of(context)
                          .pop(); // Close the dialog after update
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('No layout selected')),
                      );
                    }
                  },
                  child: const Text('Accept'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void createNewChapterPopUp(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (dialogContext) {
        // Use a StatefulBuilder to rebuild only the dialog content
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: const Text(
                "Create a new Chapter",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: sectionTitle,
                    decoration: const InputDecoration(
                      labelText: "Name",
                      hintText: "Enter chapter name",
                      hintStyle: TextStyle(color: Colors.grey),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: bookBranchGreen),
                      ),
                    ),
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey,
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    writeDataObj.addNewChapter(
                        ref, "chapters", sectionTitle.text);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Create'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.zero,
                    ),
                  ),
                ),
              ],
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
            );
          },
        );
      },
    );
  }

  void createNewEPublishingChapterPopUp(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (dialogContext) {
        // Use a StatefulBuilder to rebuild only the dialog content
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: const Text(
                "Create a new Chapter",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: sectionTitle,
                    decoration: const InputDecoration(
                      labelText: "Name",
                      hintText: "Enter chapter name",
                      hintStyle: TextStyle(color: Colors.grey),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: bookBranchGreen),
                      ),
                    ),
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey,
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    writeDataObj.addNewEPublishingChapter(
                        ref, "epublishing", sectionTitle.text);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Create'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.zero,
                    ),
                  ),
                ),
              ],
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
            );
          },
        );
      },
    );
  }

  void createNewSubsectionPopUp(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (dialogContext) {
        // Use a StatefulBuilder to rebuild only the dialog content
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: const Text(
                "Create a new Subsection",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: sectionTitle,
                    decoration: const InputDecoration(
                      labelText: "Name",
                      hintText: "Enter subsection name",
                      hintStyle: TextStyle(color: Colors.grey),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: bookBranchGreen),
                      ),
                    ),
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey,
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    writeDataSub.addNewSubsection(
                        ref, "chapters", sectionTitle.text);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Create'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.zero,
                    ),
                  ),
                ),
              ],
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
            );
          },
        );
      },
    );
  }

  void createNewEPublishingSubsectionPopUp(
      BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (dialogContext) {
        // Use a StatefulBuilder to rebuild only the dialog content
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: const Text(
                "Create a new Subsection",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    controller: sectionTitle,
                    decoration: const InputDecoration(
                      labelText: "Name",
                      hintText: "Enter subsection name",
                      hintStyle: TextStyle(color: Colors.grey),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: bookBranchGreen),
                      ),
                    ),
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey,
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    writeDataSub.addNewEPublishingSubsection(
                        ref, sectionTitle.text);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Create'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.zero,
                    ),
                  ),
                ),
              ],
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
            );
          },
        );
      },
    );
  }
}
