import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class DisplayVideoWidget extends StatefulWidget {
  final String videoUrl;

  const DisplayVideoWidget({Key? key, required this.videoUrl})
      : super(key: key);

  @override
  _DisplayVideoWidgetState createState() => _DisplayVideoWidgetState();
}

class _DisplayVideoWidgetState extends State<DisplayVideoWidget> {
  late VideoPlayerController _videoPlayerController;
  late ChewieController _chewieController;

  @override
  void initState() {
    super.initState();
    _videoPlayerController = VideoPlayerController.network(widget.videoUrl)
      ..initialize().then((_) {
        // Ensure the controller is initialized before updating the UI
        setState(() {
          _chewieController = ChewieController(
            videoPlayerController: _videoPlayerController,
            autoPlay: true,
            looping: false,
            // Use video's own aspect ratio to maintain the video size
            aspectRatio: _videoPlayerController.value.aspectRatio,
            materialProgressColors: ChewieProgressColors(
              playedColor: Colors.white,
              handleColor: Colors.white,
              backgroundColor: Colors.grey,
              bufferedColor: Colors.lightGreen,
            ),
            placeholder: Container(
              color: Colors.black,
            ),
          );
        });
      });
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(
                20.0), // Add padding around the video player
            child: Center(
              child: _videoPlayerController.value.isInitialized
                  ? Chewie(controller: _chewieController)
                  : CircularProgressIndicator(),
            ),
          ),
          Positioned(
            top: 40, // Increased padding from the top edge
            right: 20, // Consistent padding from the right edge
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }
}
