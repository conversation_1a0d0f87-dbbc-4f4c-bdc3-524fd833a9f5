import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/write_database_subsections.dart';
import 'package:web_app/database/database_logic/delete_data.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/database/database_screens/files_screen.dart';

class SubsectionsWidget extends ConsumerStatefulWidget {
  @override
  _SubsectionsWidgetState createState() => _SubsectionsWidgetState();
  final WriteDatabaseSubsections databaseObjSubsections =
      WriteDatabaseSubsections();
}

class _SubsectionsWidgetState extends ConsumerState<SubsectionsWidget> {
  @override
  Widget build(BuildContext context) {
    final chapterId = ref.watch(currentChapter);

    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('chapters')
          .doc(chapterId)
          .collection('subsections')
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData) {
          return const Center(child: Text("No subsections found"));
        }

        return ListView.builder(
          itemCount: snapshot.data!.docs.length,
          itemBuilder: (context, index) {
            DocumentSnapshot subsection = snapshot.data!.docs[index];
            String title = subsection.get('title') ?? 'Untitled Subsection';

            return Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color:
                      const Color.fromARGB(255, 44, 148, 44), // Green outline
                  width: 1.5, // Slightly thicker outline
                ),
              ),
              child: ListTile(
                leading: const Icon(
                  Icons.description_outlined,
                  color: Color.fromARGB(255, 44, 148, 44),
                  size: 20,
                ),
                title: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    letterSpacing: 0.2,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.edit,
                        color: Color.fromARGB(255, 44, 148, 44),
                        size: 20,
                      ),
                      onPressed: () {
                        // Handle edit subsection
                        ref.read(currentSubsection.notifier).state =
                            subsection.id;
                        //DeleteData().deleteSubSection(ref);
                        changeSubsectionTitlePopup(context, ref);
                      },
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.delete,
                        color: Color.fromARGB(255, 44, 148, 44),
                        size: 20,
                      ),
                      onPressed: () {
                        // Handle delete subsection
                        DeleteData().deleteSubSection(ref);
                      },
                    ),
                  ],
                ),
                onTap: () {
                  ref.read(currentSubsection.notifier).state = subsection
                      .id; // Ensure to use subsection.id to correctly identify subsection
                  Navigator.of(context)
                      .push(MaterialPageRoute(builder: (context) {
                    return Files(); // Navigate to Files page
                  }));
                },
              ),
            );
          },
        );
      },
    );
  }

  void changeSubsectionTitlePopup(BuildContext context, WidgetRef ref) {
    TextEditingController titleController = TextEditingController();
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text(
            'Change Title',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: TextField(
            controller: titleController,
            decoration: const InputDecoration(
              hintText: "Enter new title",
              hintStyle: TextStyle(color: Colors.grey),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: bookBranchGreen),
              ),
            ),
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey,
              ),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                String newTitle = titleController.text;
                if (newTitle.isNotEmpty) {
                  widget.databaseObjSubsections
                      .changeSubsectionTitle(newTitle, ref);
                  Navigator.of(context).pop(); // Close the dialog
                } else {
                  // Optionally handle empty title case
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: bookBranchGreen,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('OK'),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
      },
    );
  }
}
