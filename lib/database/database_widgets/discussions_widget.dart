import 'package:flutter/material.dart';
import 'package:web_app/readerMode/main_logic/discussions_fetch_reader.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_logic/discussions_state_management_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class ProjectDiscussionPage extends StatefulWidget {
  @override
  _ProjectDiscussionPageState createState() => _ProjectDiscussionPageState();
}

class _ProjectDiscussionPageState extends State<ProjectDiscussionPage> {
  final CommunityDiscussionsFetchReader fetcher =
      CommunityDiscussionsFetchReader();
  final UniversalWidgets universals = UniversalWidgets();

  final ValueNotifier<bool> _isDeleting = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(1.0),
              child: Container(
                height: 2.0,
                color: globals.bookBranchGreen,
              ),
            ),
            automaticallyImplyLeading:
                true, // This will automatically add a back button that works with the navigation stack
            title: Text(
              'Discussions',
              style: TextStyle(
                color: globals.bookBranchGreen,
                fontWeight: FontWeight.w600,
                fontSize: 20,
                letterSpacing: 0.5,
              ),
            ),
            iconTheme: IconThemeData(color: globals.bookBranchGreen),
          ),
          body: Consumer(builder: (context, ref, child) {
            ref.watch(discussionsProvider(ref.watch(currentProjectID)));

            return FutureBuilder<List<List<dynamic>>?>(
              future: fetcher.fetchDiscussions(ref, ''), // Empty filter
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text("Error: ${snapshot.error}"));
                } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  final discussions = snapshot.data!;
                  return ListView.builder(
                    itemCount: discussions.first.length,
                    itemBuilder: (context, index) {
                      var discussion = discussions;
                      String formattedTime = DateFormat('yyyy-MM-dd – kk:mm')
                          .format(discussion[2][index].toDate());
                      return DiscussionCard(
                        title: discussion[0][index]!,
                        content: discussion[1][index]!,
                        date: formattedTime,
                        username: discussion[6][index]!,
                        onClose: () async {
                          universals.showCustomPopupWithCheckbox(
                            context: context,
                            titleText: "Delete Discussion",
                            contentText:
                                "Are you sure you want to delete this discussion? All associated data will be lost.",
                            checkboxText: "I'm sure.",
                            cancelButtonText: "Cancel",
                            actionButtonText: "Proceed",
                            onActionPressed: () async {
                              _isDeleting.value = true; // Start deleting

                              try {
                                FirebaseFunctions functions =
                                    FirebaseFunctions.instance;
                                HttpsCallable callable = functions
                                    .httpsCallable('deleteDiscussions');
                                final result = await callable.call(
                                    {'discussionID': discussion[3][index]});
                                debugPrint(
                                    "Function called successfully: ${result.data}");
                              } catch (e) {
                                debugPrint("Failed to call function: $e");
                              } finally {
                                _isDeleting.value = false; // Finish deleting
                                ref.invalidate(discussionsProvider(
                                    ref.watch(currentProjectID)));
                                // Store and use the refresh result to avoid warning
                                final refreshResult = ref.refresh(
                                    discussionsProvider(
                                        ref.watch(currentProjectID)));
                                // We can use the refreshResult for error handling if needed
                                if (refreshResult is AsyncError) {
                                  debugPrint(
                                      "Error refreshing discussions: ${refreshResult.error}");
                                }
                              }
                            },
                          );
                        },
                      );
                    },
                  );
                } else {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Container(
                        width: double.infinity,
                        constraints: const BoxConstraints(maxWidth: 400),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(10),
                              blurRadius: 10,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                          border: Border.all(
                            color: globals.bookBranchGreen.withAlpha(50),
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.chat_outlined,
                              size: 70,
                              color: Color.fromARGB(178, 44, 148, 44),
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'No Discussions Yet',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Your subscribers have not started any discussions yet.',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'They will appear here when they do.',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }
              },
            );
          }),
        ),
        ValueListenableBuilder<bool>(
          valueListenable: _isDeleting,
          builder: (context, isDeleting, _) {
            if (isDeleting) {
              return Container(
                color: Colors.black45,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            } else {
              return const SizedBox.shrink(); // Render nothing when not loading
            }
          },
        ),
      ],
    );
  }
}

class DiscussionCard extends StatelessWidget {
  final String title;
  final String content;
  final String date;
  final String username;
  final VoidCallback onClose;
  final Widget deletingIndicator; // To handle deletion indicator

  const DiscussionCard({
    super.key,
    required this.title,
    required this.content,
    required this.date,
    required this.username,
    required this.onClose,
    this.deletingIndicator =
        const SizedBox.shrink(), // Default value for indicator
  });

  @override
  Widget build(BuildContext context) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      child: Stack(
        children: [
          // Decorative element - top gradient bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 6,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    bookBranchGreen,
                    Color.fromARGB(178, 44, 148, 44),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),
          // Decorative element - corner accent
          Positioned(
            top: 6,
            right: 0,
            child: Container(
              height: 24,
              width: 24,
              decoration: const BoxDecoration(
                color: lightGreen,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                ),
              ),
            ),
          ),
          // Main content
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // Title row with close button
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Discussion icon
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: lightGreen,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.chat_outlined,
                        color: bookBranchGreen,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Title and username in a column
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.w600,
                              color: Color.fromARGB(204, 0, 0, 0),
                              letterSpacing: 0.3,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          // Username with icon
                          Row(
                            children: [
                              const Icon(
                                Icons.person_outline,
                                size: 14,
                                color: bookBranchGreen,
                              ),
                              const SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  username,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Close button
                    if (deletingIndicator is! SizedBox)
                      deletingIndicator, // Show the indicator if not a SizedBox
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.grey),
                      onPressed: onClose,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      iconSize: 20,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Content
                Text(
                  content,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                // Date
                Row(
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      date,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
