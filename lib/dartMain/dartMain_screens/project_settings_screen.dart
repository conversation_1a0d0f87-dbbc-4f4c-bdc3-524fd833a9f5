import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_widgets/project_settings_widget.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';

class ProjectSettingsScreen extends ConsumerWidget {
  const ProjectSettingsScreen({super.key});

  static const String routeName = '/projectSettingsScreen';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: ProjectSettingsScreen.routeName)
          : NavDrawerBasic(currentRoute: ProjectSettingsScreen.routeName),
      body: const ProjectSettingsWidget(),
    );
  }
}
