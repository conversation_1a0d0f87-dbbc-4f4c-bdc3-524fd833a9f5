import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/dartMain/dartMain_widgets/applications/answers_view_widget.dart';

class ApplicationViewScreen extends ConsumerWidget {
  //static const String routeName = '/legalScreen';
  static const String routeName = '/applicantsScreen';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Received Applications',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: AnswersViewWidget(),
    );
  }
}
