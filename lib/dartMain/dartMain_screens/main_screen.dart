import 'package:flutter/material.dart';
import 'package:web_app/dartMain/dartMain_widgets/main_screen_widget.dart';

class MainScreen extends StatefulWidget {
  //const MainScreen({super.key});
  static const String routeName = '/mainScreen';
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => CreateAccount();
}

class CreateAccount extends State<MainScreen> {
  // @override
  // Widget build(BuildContext context) {
  //   return const MainScreenWidget();
  // }
  @override
  Widget build(BuildContext context) {
    return const PopScope(
      canPop: false, // <-- returning false means "don't pop"
      child: Scaffold(
        // or whatever your main widget is
        body: MainScreenWidget(),
      ),
    );
  }
}
