import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_screens/application_view_screen.dart';

class ApplicantsByProjectWidget extends ConsumerWidget {
  final QuestionnaireLogic logic = QuestionnaireLogic();
  final UniversalWidgets universals = UniversalWidgets();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(numberOfApplicationsByProject);
    return Scaffold(
      body: SafeArea(
        child: FutureBuilder<List<List<String>>>(
          future: logic.fetchApplicantsByProject(ref.watch(currentProjectID)),
          builder: (BuildContext context,
              AsyncSnapshot<List<List<String>>> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const Center(child: Text('No applicants found'));
            } else {
              List<List<String>> applicants = snapshot.data!;
              return SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: applicants
                      .map((applicant) => SizedBox(
                            height: 100, // Set a fixed height for each card
                            child: universals.buildColorfulCardWithSubtitle(
                              context,
                              applicant[0], // DisplayName as title
                              applicant[1], // timeStamp as subtitle
                              () async {
                                var applicationID =
                                    await logic.fetchApplicantionID(
                                        ref.watch(currentProjectID));
                                ref.read(currentApplicationID.notifier).state =
                                    applicationID;
                                navigatorKey.currentState!.push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        ApplicationViewScreen(),
                                  ),
                                );
                              },
                            ),
                          ))
                      .toList(),
                ),
              );
            }
          },
        ),
      ),
    );
  }
}
