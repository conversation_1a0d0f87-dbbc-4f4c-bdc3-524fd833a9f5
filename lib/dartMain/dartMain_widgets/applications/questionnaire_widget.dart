import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/ordered_item_logic.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class QuestionnaireWidget extends ConsumerWidget {
  TextEditingController controller = TextEditingController();
  QuestionnaireLogic questionnaireLogic = QuestionnaireLogic();
  UniversalWidgets universals = UniversalWidgets();
  TextEditingController questionController = TextEditingController();

  FirebaseFirestore db = FirebaseFirestore.instance;

  Future<List<OrderedItem>> fetchQuestionnaireData(WidgetRef ref) async {
    DocumentReference docRef = db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .collection('questionnaire')
        .doc('questions');
    DocumentSnapshot snapshot = await docRef.get();

    if (snapshot.exists && snapshot.data() != null) {
      // Cast snapshot.data() to Map<String, dynamic> to avoid type errors
      Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;

      // Check if 'questions' exists and is not null
      if (data.containsKey('questions') && data['questions'] != null) {
        List<dynamic> questionsData = data['questions'] as List<dynamic>;
        return questionsData
            .map((q) => OrderedItem.fromMap(q as Map<String, dynamic>))
            .toList();
      }
    }
    return []; // Return an empty list if the data is missing or incorrect
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(numberOfQuestionsByProjectProvider);
    Future<List<OrderedItem>> dataFuture = fetchQuestionnaireData(ref);

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              FutureBuilder<List<OrderedItem>>(
                future: dataFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(40.0),
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                              Color.fromARGB(255, 44, 148, 44)),
                        ),
                      ),
                    );
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            const Icon(Icons.error_outline,
                                color: Colors.red, size: 60),
                            const SizedBox(height: 16),
                            Text(
                              'Error: ${snapshot.error}',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.red,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                    return Column(
                      children: snapshot.data!
                          .map((item) => buildQuestionCard(
                              item.order, item.question, context, ref))
                          .toList(),
                    );
                  }
                  return _buildEmptyState(context);
                },
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'addQuestion',
        backgroundColor: globals.bookBranchGreen,
        foregroundColor: Colors.white,
        tooltip: "Add New Question",
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        onPressed: () async {
          universals.showCustomPopupWithTextfield(
            context,
            "Add a Question",
            "Type your new question here",
            questionController,
            () async {
              await questionnaireLogic.addQuestionnaireData(
                  ref, questionController.text);
              questionController.clear();
            },
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(25, 0, 0, 0),
              spreadRadius: 1,
              blurRadius: 6,
              offset: Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: globals.bookBranchGreen,
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.help_outline,
              size: 60,
              color: globals.bookBranchGreen,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Questions Yet',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            const Text(
              'Add questions for applicants to answer when they apply to join your project.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                universals.showCustomPopupWithTextfield(
                  context,
                  "Add a Question",
                  "Type your new question here",
                  questionController,
                  () async {
                    // Use the floating action button's approach instead
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text(
                            'Please use the + button to add a question'),
                        backgroundColor: globals.bookBranchGreen,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        margin: const EdgeInsets.all(12),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                );
              },
              icon: const Icon(Icons.add),
              label: const Text('Add First Question'),
              style: ElevatedButton.styleFrom(
                backgroundColor: globals.bookBranchGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<int> convertFutureStringToIntAndIncrement(
      Future<String> futureString) {
    return futureString.then((value) => int.parse(value) + 1);
  }

  Widget buildQuestionCard(
    int order,
    String question,
    BuildContext context,
    WidgetRef ref,
  ) {
    TextEditingController localController =
        TextEditingController(text: question);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(25, 0, 0, 0),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: globals.bookBranchGreen,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question number and icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(25, 44, 148, 44),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Q$order',
                    style: TextStyle(
                      color: globals.bookBranchGreen,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.help_outline,
                  color: globals.bookBranchGreen,
                  size: 20,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Editable field
            TextField(
              controller: localController,
              minLines: 2,
              maxLines: 5,
              keyboardType: TextInputType.multiline,
              decoration: InputDecoration(
                hintText: "Enter your question here",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: globals.bookBranchGreen,
                    width: 1.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: globals.bookBranchGreen,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ),

            const SizedBox(height: 16),

            // Button row
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // UPDATE BUTTON
                ElevatedButton.icon(
                  icon: const Icon(Icons.check, size: 18),
                  label: const Text("Update"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: globals.bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 10.0,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    elevation: 2,
                  ),
                  onPressed: () {
                    questionnaireLogic.updateQuestionnaireData(
                        ref, order, localController.text);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Row(
                          children: [
                            Icon(Icons.check_circle, color: Colors.white),
                            SizedBox(width: 12),
                            Text('Question updated successfully'),
                          ],
                        ),
                        backgroundColor: globals.bookBranchGreen,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        margin: const EdgeInsets.all(12),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                ),

                const SizedBox(width: 12),

                // REMOVE BUTTON
                ElevatedButton.icon(
                  icon: const Icon(Icons.delete_outline, size: 18),
                  label: const Text("Remove"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 10.0,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    elevation: 2,
                  ),
                  onPressed: () {
                    // Show confirmation dialog
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text(
                            'Remove Question',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              color: Colors.black87,
                            ),
                          ),
                          content: const Text(
                            'Are you sure you want to remove this question?',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                          ),
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              color: globals.bookBranchGreen,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  color: globals.bookBranchGreen,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                questionnaireLogic.deleteQuestionnaireData(
                                    ref, order);
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Row(
                                      children: [
                                        Icon(Icons.check_circle,
                                            color: Colors.white),
                                        SizedBox(width: 12),
                                        Text('Question removed successfully'),
                                      ],
                                    ),
                                    backgroundColor: Colors.red.shade700,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    margin: const EdgeInsets.all(12),
                                    duration: const Duration(seconds: 2),
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6.0),
                                ),
                              ),
                              child: const Text('Remove'),
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
