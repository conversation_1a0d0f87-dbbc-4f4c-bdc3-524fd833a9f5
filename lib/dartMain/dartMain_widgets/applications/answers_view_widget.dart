import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/ordered_item_logic.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class AnswersViewWidget extends ConsumerWidget {
  final QuestionnaireLogic questionnaireLogic = QuestionnaireLogic();
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<List<OrderedItem>> dataFuture = questionnaireLogic
        .fetchApplicantResponsesByID(ref.watch(currentApplicationID));

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 10),
              FutureBuilder<List<OrderedItem>>(
                future: dataFuture,
                builder: (BuildContext context,
                    AsyncSnapshot<List<OrderedItem>> snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const SizedBox(
                      height: 100,
                      child: Center(child: CircularProgressIndicator()),
                    );
                  } else if (snapshot.hasError) {
                    return SizedBox(
                      height: 100,
                      child: Center(child: Text('Error: ${snapshot.error}')),
                    );
                  } else if (snapshot.hasData) {
                    List<OrderedItem> data = snapshot.data!;
                    List<TextEditingController> controllers = data
                        .map((item) => TextEditingController(text: item.answer))
                        .toList();

                    // Assuming 'universals' is an instance of UniversalWidgets
                    UniversalWidgets universals = UniversalWidgets();

                    return Column(
                      children: data
                          .map((item) => buildTextFieldWithLabel(
                                item.question,
                                item.answer ?? '',
                                controllers[data.indexOf(item)],
                                context,
                                universals, // Pass the UniversalWidgets instance
                              ))
                          .toList(),
                    );
                  } else {
                    return const SizedBox(); // Handle the case when there is no data
                  }
                },
              ),
              const SizedBox(height: 10),
              UniversalWidgets().buildButtonFlatWidth(
                "Accept User",
                true,
                () async {
                  isLoading.value = true; // Start deleting

                  FirebaseFunctions functions = FirebaseFunctions.instance;

                  // Get a reference to the callable function
                  HttpsCallable callable =
                      functions.httpsCallable('acceptUserApplication');

                  // Call the function and pass parameters
                  final data = await callable.call({
                    'applicationID': ref.watch(currentApplicationID),
                    'projectID': ref.watch(currentProjectID)
                  });

                  // Handle the function's response
                  print("Function called successfully: ${data.data}");

                  ref.refresh(numberOfApplicationsByProject);
                  ref.refresh(numberOfProjectsWithApplications);
                  isLoading.value = false; // Finish deleting

                  Navigator.pop(context);
                },
                globals.bookBranchGreen,
                Colors.white,
                context,
                1.0,
              ),
              UniversalWidgets().buildButtonFlatWidth(
                "Deny User",
                true,
                () async {
                  // denyUserApplication ===***
                  isLoading.value = true; // Start deleting

                  FirebaseFunctions functions = FirebaseFunctions.instance;

                  // Get a reference to the callable function
                  HttpsCallable callable =
                      functions.httpsCallable('denyUserApplication');

                  // Call the function and pass parameters
                  final data = await callable.call({
                    'applicationID': ref.watch(currentApplicationID),
                  });

                  // Handle the function's response
                  print("Function called successfully: ${data.data}");

                  ref.refresh(numberOfApplicationsByProject);
                  ref.refresh(numberOfProjectsWithApplications);

                  isLoading.value = false; // Finish deleting

                  Navigator.pop(context);
                },
                Colors.red,
                Colors.white,
                context,
                1.0, // Full width
              ),
              const SizedBox(height: 10),
              ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (context, loading, child) {
                  if (loading) {
                    return Container(
                      color: Colors.black45,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  } else {
                    return const SizedBox
                        .shrink(); // Render nothing when not loading
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildTextFieldWithLabel(
    String label,
    String initialValue,
    TextEditingController localController,
    BuildContext context,
    UniversalWidgets universals, // Using the modified widget method
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          universals.buildEditableMinimalistFieldHintTextToolTipReadAnswer(
            label,
            localController,
            initialValue,
            maxLines: null, // Set to null or a higher number for expansion
          ),
        ],
      ),
    );
  }
}
