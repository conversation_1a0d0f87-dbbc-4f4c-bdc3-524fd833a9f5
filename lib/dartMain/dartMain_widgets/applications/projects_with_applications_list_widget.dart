import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_screens/applicants_by_project_screen.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class ProjectsWithApplicationsListWidget extends ConsumerWidget {
  final QuestionnaireLogic logic = QuestionnaireLogic();
  final UniversalWidgets universals = UniversalWidgets();

  ProjectsWithApplicationsListWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(numberOfProjectsWithApplications);
    return Scaffold(
      body: SafeArea(
        child: FutureBuilder<List<dynamic>>(
          future: Future.wait([
            logic.fetchProjectsWithApplicationsByUser(),
            logic.fetchProjectIDsWithApplicationsByUser()
          ]),
          builder:
              (BuildContext context, AsyncSnapshot<List<dynamic>> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            } else if (!snapshot.hasData ||
                snapshot.data!.isEmpty ||
                snapshot.data![0].isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(maxWidth: 400),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: globals.bookBranchGreen.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Card header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: globals.bookBranchGreen.withAlpha(15),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.person_outline_rounded,
                                color: globals.bookBranchGreen,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              const Text(
                                'Applications',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Card content
                        Padding(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              // Illustration
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.description_outlined,
                                  size: 40,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                              const SizedBox(height: 24),

                              // Message
                              const Text(
                                'No Applications Yet',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'You have not received any applications yet. They will appear here when you do.',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade700,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              List<String> projectNames = snapshot.data![0];
              List<String> projectIDs = snapshot.data![1];
              return SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: projectNames.asMap().entries.map((entry) {
                    int index = entry.key;
                    String projectName = entry.value;
                    return SizedBox(
                      height: 100, // Set a fixed height for each card
                      child: universals.buildColorfulCard(
                        context,
                        projectName,
                        () {
                          // Assign the corresponding projectID based on the index
                          ref.read(currentProjectID.notifier).state =
                              projectIDs[index];
                          // Navigate to the new page
                          navigatorKey.currentState!.push(
                            MaterialPageRoute(
                              builder: (context) => ApplicantsByProjectScreen(),
                            ),
                          );
                        },
                      ),
                    );
                  }).toList(),
                ),
              );
            }
          },
        ),
      ),
    );
  }
}
