import 'package:flutter/material.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class HelpWidget extends StatelessWidget {
  const HelpWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Container(
            width: double.infinity,
            constraints: const BoxConstraints(maxWidth: 400),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: globals.bookBranchGreen.withAlpha(50),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Support icon
                const Icon(
                  Icons.support_agent,
                  size: 70,
                  color: Color.fromARGB(178, 44, 148, 44),
                ),
                const SizedBox(height: 16),
                // Title
                const Text(
                  'Need Help?',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),
                // Support message
                const Text(
                  'For assistance of any kind, please reach out to us at:',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color.fromARGB(178, 102, 102, 102),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                // Email address
                SelectableText(
                  '<EMAIL>',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: globals.bookBranchGreen,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // Response time message
                const Text(
                  'We typically respond within 24 hours.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color.fromARGB(178, 102, 102, 102),
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
