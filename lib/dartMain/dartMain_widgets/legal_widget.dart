import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

UniversalWidgets universals = UniversalWidgets();

class LegalWidget extends StatefulWidget {
  const LegalWidget({super.key});

  @override
  State<LegalWidget> createState() => _LegalWidgetState();
}

class _LegalWidgetState extends State<LegalWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<List<Map<String, dynamic>>> _fetchLegalDocuments() async {
    try {
      QuerySnapshot querySnapshot =
          await _firestore.collection('legalFiles').get();
      return querySnapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .toList();
    } catch (e) {
      debugPrint('Error fetching legal documents: $e');
      return [];
    }
  }

  void _openPDFViewer(String pdfUrl) {
    Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => MobilePdfViewer(pdfUrl: pdfUrl),
    ));
  }

  Widget _buildModernLegalCard(
      BuildContext context, String title, VoidCallback onTap) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 100,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: bookBranchGreen,
            width: 1.5,
          ),
        ),
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 6,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148, 44),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 6,
              right: 0,
              child: Container(
                height: 24,
                width: 24,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                  ),
                ),
              ),
            ),
            // Document icon
            Positioned(
              top: 30,
              left: 16,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.description_outlined,
                  color: bookBranchGreen,
                  size: 24,
                ),
              ),
            ),
            // Title
            Positioned(
              top: 30,
              left: 72,
              right: 16,
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color.fromARGB(204, 0, 0, 0),
                  letterSpacing: 0.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Tap to view text
            const Positioned(
              bottom: 12,
              right: 16,
              child: Row(
                children: [
                  Text(
                    'Tap to view',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: FutureBuilder<List<Map<String, dynamic>>>(
          future: _fetchLegalDocuments(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return const Center(child: Text('Error fetching documents'));
            } else if (snapshot.hasData && snapshot.data!.isEmpty) {
              return const Center(child: Text('No documents found'));
            } else {
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: snapshot.data!.map((document) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 8.0, horizontal: 16.0),
                    child: _buildModernLegalCard(
                      context,
                      document['title'],
                      () => _openPDFViewer(document['url']),
                    ),
                  );
                }).toList(),
              );
            }
          },
        ),
      ),
    );
  }
}
