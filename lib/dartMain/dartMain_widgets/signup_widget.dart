import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_screens/legal_screen.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_screens/email_verification_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/main_screen.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:getwidget/getwidget.dart';

class SignUpPage extends ConsumerStatefulWidget {
  const SignUpPage({Key? key}) : super(key: key);

  @override
  _SignUpPageState createState() => _SignUpPageState();
}

class _SignUpPageState extends ConsumerState<SignUpPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  bool _isBookBranchPlusSelected =
      false; // State to manage which sign up option is selected

  AuthenticationLogic authObject = AuthenticationLogic();

  final FirebaseAuth auth = FirebaseAuth.instance;
  FirebaseFirestore db = FirebaseFirestore.instance;

  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Widget _textField(
      String label, TextEditingController controller, bool isPassword) {
    return GFTextField(
      controller: controller,
      textAlign: TextAlign.start,
      obscureText: isPassword,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Colors.grey),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.shade300, width: 1.0),
          borderRadius: BorderRadius.circular(4),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: globals.bookBranchGreen, width: 2.0),
          borderRadius: BorderRadius.circular(4),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        border: const OutlineInputBorder(),
      ),
      cursorColor: globals.bookBranchGreen,
    );
  }

  Widget _buildSignUpOptionButton(String title, bool isSelected) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _isBookBranchPlusSelected = (title == "BookBranch+");
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected ? globals.bookBranchGreen : Colors.transparent,
            border: Border.all(color: globals.bookBranchGreen),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? Colors.white : globals.bookBranchGreen,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(1.0),
              child: Container(
                height: 2.0,
                color: globals.bookBranchGreen,
              ),
            ),
            leading: IconButton(
              icon: Icon(Icons.arrow_back,
                  color: globals.bookBranchGreen, size: 24),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              'Create Account',
              style: TextStyle(
                color: globals.bookBranchGreen,
                fontWeight: FontWeight.w600,
                fontSize: 20,
                letterSpacing: 0.5,
              ),
            ),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome header
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Join BookBranch',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: globals.bookBranchGreen,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create your account to get started.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                _textField("Email", _emailController, false),
                const SizedBox(height: 16),
                _textField("Password", _passwordController, true),
                const SizedBox(height: 16),
                _textField(
                    "Confirm Password", _confirmPasswordController, true),
                const SizedBox(height: 24),
                // Account type selection header
                Text(
                  'Select Account Type',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildSignUpOptionButton(
                        "BookBranch", !_isBookBranchPlusSelected),
                    const SizedBox(width: 10),
                    _buildSignUpOptionButton(
                        "BookBranch+", _isBookBranchPlusSelected),
                  ],
                ),
                const SizedBox(height: 30),
                // Google sign-up button
                Center(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: const [
                        BoxShadow(
                          color: Color.fromRGBO(0, 0, 0, 0.05),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: buildGoogleSignUpButton(() {
                      _handleGoogleSignUp();
                    }),
                  ),
                ),
                const SizedBox(height: 40),
                // Divider with "or" text
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        height: 1,
                        color: Colors.grey.shade300,
                      ),
                    ),
                    Text(
                      "or",
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        height: 1,
                        color: Colors.grey.shade300,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                _signupButton(context),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border.all(color: Colors.grey.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _isBookBranchPlusSelected
                        ? "BookBranch+ gives users 100 GBs of storage, unlimited project creation, and custom subscription settings. Additionally, tailor your community and its application process to fit your interests through the creation of custom made questionnaires."
                        : "BookBranch gives you access to user created content and discussions. Subscribe to any and all material on the platform and find your community.",
                    style: TextStyle(color: Colors.grey[600], fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: SafeArea(
            minimum: const EdgeInsets.only(bottom: 8.0),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10, 10, 10, 16),
              child: Text.rich(
                TextSpan(
                  text: 'By signing up, you agree to our ',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  children: <TextSpan>[
                    TextSpan(
                      text: 'Terms of Service',
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => LegalScreen()),
                          );
                        },
                    ),
                    TextSpan(
                      text: ' and ',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    TextSpan(
                      text: 'Privacy Policy',
                      style: const TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Navigator.pushNamed(context, '/legalScreen');
                        },
                    ),
                    TextSpan(
                      text: '.',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
        if (_isLoading)
          Container(
            color: Colors.black45, // semi-transparent background
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Custom animated loader
                  GFLoader(
                    type: GFLoaderType.circle,
                    loaderColorOne: globals.bookBranchGreen,
                    loaderColorTwo: Colors.white,
                    loaderColorThree: globals.bookBranchGreen,
                    size: 50.0,
                  ),
                  const SizedBox(height: 20),
                  // Animated text
                  TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0.0, end: 1.0),
                    duration: const Duration(milliseconds: 500),
                    builder: (context, value, child) {
                      return Opacity(
                        opacity: value,
                        child: Transform.translate(
                          offset: Offset(0, 20 * (1 - value)),
                          child: const Text(
                            'Creating account...',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _signupButton(BuildContext context) {
    return GFButton(
      onPressed: () async {
        if (_passwordController.text != _confirmPasswordController.text) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Passwords do not match.')),
          );
          return;
        }

        setState(() {
          _isLoading = true; // Start loading immediately
        });

        // Store context before async operations
        final currentContext = context;

        try {
          User? user;
          if (_isBookBranchPlusSelected == true) {
            // Call handleSignUp for BookBranch+
            user = await authObject.handleSignUpBookBranchPlus(
              _emailController.text,
              _passwordController.text,
              _confirmPasswordController.text,
              ref,
            );
          } else if (_isBookBranchPlusSelected == false) {
            // Call handleSignUp for BookShelf
            user = await authObject.handleSignUp(
              _emailController.text,
              _passwordController.text,
              ref,
            );
          }

          // Only navigate if signup was successful and widget is still mounted
          if (user != null && mounted && currentContext.mounted) {
            // Navigate to email verification screen instead of main screen
            Navigator.push(
              currentContext,
              MaterialPageRoute(
                builder: (context) => EmailVerificationScreen(user: user!),
              ),
            );
          }
        } catch (e) {
          // Handle the error here, e.g., show a dialog or a snackbar
          if (mounted && currentContext.mounted) {
            ScaffoldMessenger.of(currentContext).showSnackBar(
              SnackBar(content: Text('Error signing up: $e')),
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isLoading =
                  false; // Stop loading once process is complete or fails
            });
          }
        }
      },
      text: "Sign Up",
      textStyle: const TextStyle(
          fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
      color: globals.bookBranchGreen,
      fullWidthButton: true,
      size: GFSize.LARGE,
      shape: GFButtonShape.standard,
      // Add a subtle hover animation
      hoverColor: globals.bookBranchGreen.withAlpha(200),
      // Add a nice elevation effect
      elevation: 2,
      highlightElevation: 4,
      splashColor: const Color.fromRGBO(255, 255, 255, 0.3),
      padding: const EdgeInsets.symmetric(vertical: 10),
    );
  }

  Widget buildGoogleSignUpButton(VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Image.asset(
        'assets/google/signin_logo.png',
        width: 240,
        height: 50,
      ),
    );
  }

  // Handle Google sign-up with proper context handling
  Future<void> _handleGoogleSignUp() async {
    // Store context before async operations
    final currentContext = context;

    setState(() {
      _isLoading = true; // Start loading immediately
    });

    try {
      User? user;
      if (_isBookBranchPlusSelected) {
        // Call Google sign-up for BookBranch+
        user = await authObject.signUpWithGooglePlus(ref);
      } else {
        // Call Google sign-up for BookBranch
        user = await authObject.signUpWithGoogle(ref);
      }

      // Check if widget is still mounted before proceeding
      if (!mounted || !currentContext.mounted) return;

      if (user != null) {
        // Google accounts are automatically verified, go directly to main screen
        Navigator.push(
          currentContext,
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      } else {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          const SnackBar(
            content: Text('Failed to sign up with Google. Try again.'),
          ),
        );
      }
    } catch (e) {
      if (!mounted || !currentContext.mounted) return;

      ScaffoldMessenger.of(currentContext).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false; // Stop loading once process is complete or fails
        });
      }
    }
  }
}
