import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:web_app/dartMain/dartMain_logic/create_new_user.dart';
import 'package:web_app/dartMain/dartMain_screens/project_details_screen.dart';
import 'package:web_app/dartMain/dartMain_logic/projects_create_logic.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/fetch_main.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/dartMain/dartMain_logic/dartmain_logic_fetch/projects_create_logic_fetch.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class ProjectsWidget extends ConsumerWidget {
  ProjectsWidget({super.key});
  static const String routeName = '/projectsScreen';

  final ProjectsCreateLogic projObj = ProjectsCreateLogic();
  final CreateNewUser firestoreObj = CreateNewUser();
  final WriteDatabase writeDataObj = WriteDatabase();
  final FetchDatabase fetch = FetchDatabase();
  final fetchMain = FetchMain();
  final UniversalWidgets universals = UniversalWidgets();
  final ProjectsCreateLogicFetch projFetch = ProjectsCreateLogicFetch();

  final TextEditingController projectName = TextEditingController();
  final TextEditingController projectDescription = TextEditingController();

  String layoutChoice = 'Chapters';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final projectCountAsyncValue = ref.watch(projectCountProvider);

    return Scaffold(
      floatingActionButton: FloatingActionButton(
        backgroundColor: globals.bookBranchGreen,
        foregroundColor: Colors.white,
        elevation: 4,
        highlightElevation: 8,
        tooltip: "Create New Project",
        onPressed: () {
          createProjectDialogue(context, ref);
        },
        child: const Icon(Icons.add),
      ),
      body: projectCountAsyncValue.when(
        data: (futureData) {
          // The future completed successfully, but it's another Future
          return FutureBuilder<int>(
            future: futureData,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              } else if (!snapshot.hasData || snapshot.data == 0) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.folder_outlined,
                        size: 70,
                        color: Color.fromARGB(178, 44, 148, 44),
                      ),
                      SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 32.0),
                        child: Text(
                          'You have not created any projects yet. Hit the + button to get started.',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: Color.fromARGB(204, 0, 0, 0),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                );
              }

              // If we have projects
              // Conditional rendering based on platform or other logic.
              return kIsWeb
                  ? projectDisplayMenu(
                      projFetch.fetchAllProjectNamesByUser("Project Name"), ref)
                  : projectDisplayMenuMobile(
                      projFetch.fetchAllProjectNamesByUser("Project Name"),
                      ref);
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  FutureBuilder<List<dynamic>> projectDisplayMenu(
      Future<List<String>> futureValues, WidgetRef ref) {
    return FutureBuilder<List<dynamic>>(
      future: Future.wait([
        futureValues,
        projFetch.fetchAllProjectIDsByUser(),
        fetchMain.fetchAllProjectThumbnailUrlsByCreator(),
        projFetch.fetchAllProjectVisibleIDsByUser()
      ]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (snapshot.hasError) {
          return Center(
            child: Text('Error: ${snapshot.error}'),
          );
        } else if (!snapshot.hasData ||
            snapshot.data!.isEmpty ||
            snapshot.data![0].isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.folder_outlined,
                  size: 70,
                  color: Color.fromARGB(178, 44, 148, 44),
                ),
                SizedBox(height: 20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'You have not created any projects yet. Hit the + button to get started.',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Color.fromARGB(204, 0, 0, 0),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20),
              ],
            ),
          );
        } else {
          List<String> values = snapshot.data![0];
          List<String> idList = snapshot.data![1];
          List<String> thumbnailUrls = snapshot.data![2];
          List<String> visibleProjectIDs = snapshot.data![3];

          ref.watch(projectCountProvider);

          return Wrap(
            direction: Axis.horizontal,
            spacing: 15.0,
            runSpacing: 25.0,
            children: [
              for (int i = 0; i < values.length; i++)
                projectCard(context, ref, values[i], idList[i],
                    thumbnailUrls[i], visibleProjectIDs[i])
            ],
          );
        }
      },
    );
  }

  Widget projectCard(BuildContext context, WidgetRef ref, String projectName,
      String projectId, String thumbnailUrl, String visibleProjectId) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);
    bool isDefaultThumbnail = thumbnailUrl == 'default' || thumbnailUrl.isEmpty;

    return Container(
      width: 280,
      height: 200,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(25, 0, 0, 0),
            spreadRadius: 1,
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            ref.read(currentProjectName.notifier).state = projectName;
            ref.read(currentProjectID.notifier).state = projectId;
            ref.read(currentProjectVisibleIDProvider.notifier).state =
                visibleProjectId;
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => ProjectDetails(),
              ),
            );
          },
          child: Stack(
            children: [
              // Decorative element - top gradient bar
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 8,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        bookBranchGreen,
                        Color.fromARGB(178, 44, 148, 44),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                ),
              ),
              // Decorative element - corner accent
              Positioned(
                top: 8,
                right: 0,
                child: Container(
                  height: 40,
                  width: 40,
                  decoration: const BoxDecoration(
                    color: lightGreen,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(40),
                    ),
                  ),
                ),
              ),
              // Main content
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Project icon or thumbnail and name
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Project icon or thumbnail
                        isDefaultThumbnail
                            ? Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: lightGreen,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.folder_outlined,
                                  color: bookBranchGreen,
                                  size: 30,
                                ),
                              )
                            : ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  thumbnailUrl,
                                  width: 60,
                                  height: 60,
                                  fit: BoxFit.cover,
                                ),
                              ),
                        const SizedBox(width: 16),
                        // Project name
                        Expanded(
                          child: Text(
                            projectName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color.fromARGB(204, 0, 0, 0),
                              letterSpacing: 0.3,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Owner badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 5,
                      ),
                      decoration: BoxDecoration(
                        color: lightGreen,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: const Color.fromARGB(76, 44, 148, 44),
                          width: 1,
                        ),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.person_outline,
                            size: 16,
                            color: bookBranchGreen,
                          ),
                          SizedBox(width: 6),
                          Text(
                            'Project Owner',
                            style: TextStyle(
                              fontSize: 14,
                              color: bookBranchGreen,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    // View details button
                    Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: bookBranchGreen,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'View Details',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(width: 4),
                            Icon(
                              Icons.arrow_forward,
                              size: 14,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  FutureBuilder<List<dynamic>> projectDisplayMenuMobile(
      Future<List<String>> futureValues, WidgetRef ref) {
    ref.watch(projectCountProvider);

    return FutureBuilder<List<dynamic>>(
      future: Future.wait([
        futureValues,
        projFetch.fetchAllProjectIDsByUser(),
        fetchMain.fetchAllProjectThumbnailUrlsByCreator(),
        projFetch.fetchAllProjectVisibleIDsByUser()
      ]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (snapshot.hasError) {
          return Center(
            child: Text('Error: ${snapshot.error}'),
          );
        } else if (!snapshot.hasData ||
            snapshot.data!.isEmpty ||
            snapshot.data![0].isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.folder_outlined,
                  size: 70,
                  color: Color.fromARGB(178, 44, 148, 44),
                ),
                SizedBox(height: 20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.0),
                  child: Text(
                    'You have not created any projects yet. Hit the + button to get started.',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Color.fromARGB(204, 0, 0, 0),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20),
              ],
            ),
          );
        } else {
          List<String> values = snapshot.data![0];
          List<String> idList = snapshot.data![1];
          List<String> thumbnailUrls = snapshot.data![2];
          List<String> visibleProjectIDs = snapshot.data![3];

          return GridView.builder(
            padding: const EdgeInsets.all(8),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 1, // Number of items per row
              crossAxisSpacing: 15, // Horizontal space between items
              mainAxisSpacing: 15, // Vertical space between items
              childAspectRatio: 3 / 1.5, // Aspect ratio of the cards
            ),
            itemCount: values.length,
            itemBuilder: (context, i) {
              // Check if the thumbnail URL is 'default' or not available
              bool isDefaultThumbnail =
                  thumbnailUrls[i] == 'default' || thumbnailUrls[i].isEmpty;

              const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
              const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

              return Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: Color.fromARGB(25, 0, 0, 0),
                      spreadRadius: 1,
                      blurRadius: 6,
                      offset: Offset(0, 3),
                    ),
                  ],
                  border: Border.all(
                    color: bookBranchGreen,
                    width: 1.5,
                  ),
                ),
                width: double.infinity,
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      ref.read(currentProjectName.notifier).state = values[i];
                      ref.read(currentProjectID.notifier).state = idList[i];
                      ref.read(currentProjectVisibleIDProvider.notifier).state =
                          visibleProjectIDs[i];
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => ProjectDetails(),
                        ),
                      );
                    },
                    child: Stack(
                      children: [
                        // Decorative element - top gradient bar
                        Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            height: 8,
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  bookBranchGreen,
                                  Color.fromARGB(178, 44, 148, 44),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                            ),
                          ),
                        ),
                        // Decorative element - corner accent
                        Positioned(
                          top: 8,
                          right: 0,
                          child: Container(
                            height: 40,
                            width: 40,
                            decoration: const BoxDecoration(
                              color: lightGreen,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(40),
                              ),
                            ),
                          ),
                        ),
                        // Main content
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Project icon or thumbnail
                              isDefaultThumbnail
                                  ? Container(
                                      width: 50,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: lightGreen,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Icon(
                                        Icons.folder_outlined,
                                        color: bookBranchGreen,
                                        size: 25,
                                      ),
                                    )
                                  : ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        thumbnailUrls[i],
                                        width: 50,
                                        height: 50,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                              const SizedBox(width: 12),
                              // Project name and owner badge
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      values[i],
                                      style: const TextStyle(
                                        fontSize: 17,
                                        fontWeight: FontWeight.w600,
                                        color: Color.fromARGB(204, 0, 0, 0),
                                        letterSpacing: 0.3,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 8),
                                    // Owner badge
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 3,
                                      ),
                                      decoration: BoxDecoration(
                                        color: lightGreen,
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color: const Color.fromARGB(
                                              76, 44, 148, 44),
                                          width: 1,
                                        ),
                                      ),
                                      child: const Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.person_outline,
                                            size: 14,
                                            color: bookBranchGreen,
                                          ),
                                          SizedBox(width: 4),
                                          Text(
                                            'Owner',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: bookBranchGreen,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
            physics: const BouncingScrollPhysics(),
          );
        }
      },
    );
  }

  void createProjectDialogue(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);
    bool isPublic = true; // Default to public
    bool isChecked = false; // Default state for the checkbox

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              title: Stack(
                children: [
                  // Decorative element - top gradient bar
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 4,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            bookBranchGreen,
                            Color.fromARGB(178, 44, 148, 44),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.only(top: 8.0),
                    child: Text(
                      'Create a New Project',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 20,
                        color: Color.fromARGB(204, 0, 0, 0),
                        letterSpacing: 0.3,
                      ),
                    ),
                  ),
                ],
              ),
              titlePadding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: projectName,
                      decoration: InputDecoration(
                        labelText: "Project Name",
                        labelStyle: const TextStyle(color: Colors.grey),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: bookBranchGreen),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: projectDescription,
                      decoration: InputDecoration(
                        labelText: "Description",
                        labelStyle: const TextStyle(color: Colors.grey),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: bookBranchGreen),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(fontSize: 16),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      "Project Visibility",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color.fromARGB(204, 0, 0, 0),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                isPublic = true;
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isPublic
                                  ? bookBranchGreen
                                  : Colors.grey.shade200,
                              foregroundColor: isPublic
                                  ? Colors.white
                                  : Colors.grey.shade700,
                              elevation: isPublic ? 2 : 0,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'Public',
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                isPublic = false;
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: !isPublic
                                  ? bookBranchGreen
                                  : Colors.grey.shade200,
                              foregroundColor: !isPublic
                                  ? Colors.white
                                  : Colors.grey.shade700,
                              elevation: !isPublic ? 2 : 0,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'Private',
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: lightGreen,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color.fromARGB(76, 44, 148, 44),
                          width: 1,
                        ),
                      ),
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.info_outline,
                            color: bookBranchGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              isPublic
                                  ? "Public projects are accessible to all BookBranch users who subscribe."
                                  : "Private projects require users to apply and be approved by you.",
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color.fromARGB(204, 0, 0, 0),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext innerContext) {
                            return AlertDialog(
                              title: const Text(
                                "Project Visibility Information",
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 18,
                                  color: Color.fromARGB(204, 0, 0, 0),
                                ),
                              ),
                              content: const SingleChildScrollView(
                                child: ListBody(
                                  children: <Widget>[
                                    Text(
                                      "Public: A public project is accessible to all BookBranch users subscribed to the project. They can view all content and participate in and create discussions. Additionally, a public project is always accessible and searchable within the Marketplace. As the creator of a public project, you do not have control over who subscribes, however, your project is likely to garner greater visibility.",
                                      style: TextStyle(height: 1.4),
                                    ),
                                    SizedBox(height: 16),
                                    Text(
                                      "Private: A private project is only accessible to those users who have completed the application process set up by you, the creator, and subsequently been approved. Your application process can vary in length and complexity but always consists of one or a series of questions requiring written responses. As a creator, you will need to create a Questionnaire which can be done within Project Settings before anyone can gain access to your material. Making your project private allows you the freedom to tailor your community to only those individuals you deem qualified to contribute and allows greater cohesion among the group members.",
                                      style: TextStyle(height: 1.4),
                                    ),
                                    SizedBox(height: 16),
                                    Text(
                                      "Notice: This action cannot be undone. Once a project is created, the visibility setting cannot be changed. Please ensure you have selected the correct visibility setting before proceeding.",
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        height: 1.4,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              actions: <Widget>[
                                TextButton(
                                  child: const Text(
                                    "Close",
                                    style: TextStyle(color: bookBranchGreen),
                                  ),
                                  onPressed: () {
                                    Navigator.of(innerContext).pop();
                                  },
                                ),
                              ],
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            );
                          },
                        );
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: bookBranchGreen,
                        padding: EdgeInsets.zero,
                        alignment: Alignment.centerLeft,
                      ),
                      child: const Text("Learn more about project visibility"),
                    ),
                    const SizedBox(height: 16),
                    CheckboxListTile(
                      title: const Text(
                        "I understand and agree that visibility cannot be changed later.",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      value: isChecked,
                      onChanged: (bool? value) {
                        setState(() {
                          isChecked = value ?? false;
                        });
                      },
                      activeColor: bookBranchGreen,
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isChecked &&
                                projectName.text.isNotEmpty &&
                                projectDescription.text.isNotEmpty
                            ? () {
                                projObj.addProjectData(
                                  projectName.text,
                                  projectDescription.text,
                                  isPublic ? "public" : "private",
                                  ref,
                                );
                                Navigator.pop(dialogContext);
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: bookBranchGreen,
                          foregroundColor: Colors.white,
                          disabledBackgroundColor: Colors.grey.shade300,
                          disabledForegroundColor: Colors.grey.shade500,
                          elevation: 2,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Create Project',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              contentPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            );
          },
        );
      },
    ).then((_) {
      // Clear the text fields after the dialog is dismissed
      projectName.clear();
      projectDescription.clear();
    });
  }
}
