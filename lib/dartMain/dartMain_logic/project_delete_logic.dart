import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/main.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/fetch_database.dart';

class ProjectsDeleteLogic {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  final FetchDatabase fetchDatabase = FetchDatabase();

// delete files pertaining to project in firestore
  Future<List<String>> deleteDocumentsMatchingProjectID(
      String projectID) async {
    List<String> urlList = [];
    try {
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection("data")
          .where("projectID", isEqualTo: projectID)
          .get();
      for (QueryDocumentSnapshot documentSnapshot in snapshot.docs) {
        Map<String, dynamic> data =
            documentSnapshot.data() as Map<String, dynamic>;
        urlList.add(data['File URL']);
        await FirebaseFirestore.instance
            .collection("data")
            .doc(documentSnapshot.id)
            .delete();
      }
    } catch (e) {
      print('Error deleting documents: $e');
    }
    return urlList;
  }

  // delete pending applications for project
  Future<void> deletePendingApplicationsForProject(String projectID) async {
    try {
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection("applications")
          .where("projectID", isEqualTo: projectID)
          .get();
      for (QueryDocumentSnapshot documentSnapshot in snapshot.docs) {
        await FirebaseFirestore.instance
            .collection("applications")
            .doc(documentSnapshot.id)
            .delete();
      }
    } catch (e) {
      print('Error deleting pending applications: $e');
    }
  }

  Future<void> unsubscribeAllUsersFromProject(String projectId) async {
    // Reference to the collection
    final collectionRef = db.collection('projectSubscriptions');
    // Fetch all documents; caution with large collections
    final querySnapshot = await collectionRef.get();

    // Prepare a batch write to handle multiple field deletions within documents
    WriteBatch batch = db.batch();

    for (var doc in querySnapshot.docs) {
      if (doc.data().containsKey(projectId)) {
        // Check if the document contains the project ID
        final docRef = collectionRef.doc(doc.id);
        batch.update(docRef, {projectId: FieldValue.delete()});
      }
    }

    // Commit the batch
    await batch.commit().catchError((error) {
      print('Error unsubscribing all users from project: $error');
    });
  }

  Future<void> deleteProject(String projectID, WidgetRef ref) async {
    // before doing anything else, nullify the 'Storage Used' variable
    // corresponding to the project being deleted within 'users'
    final User? user = auth.currentUser;
    final uid = user?.uid;

    final storageUsedByProject =
        await fetchDatabase.fetchUploadedBytesByProject(ref);

    QuerySnapshot userSnapshot =
        await db.collection('users').where('uid', isEqualTo: uid).get();

    // update storage used in 'users' collection
    for (var doc in userSnapshot.docs) {
      int storageUsed = doc.get('Storage Used');
      int newStorageUsed = storageUsed - storageUsedByProject;
      await db
          .collection('users')
          .doc(doc.id)
          .update({'Storage Used': newStorageUsed});
    }

// ---------------------------------------------------------------------------------------------------

    // Reference to the parent document's first-level subcollection
    final firstLevelSubcollectionRef = FirebaseFirestore.instance
        .collection('projects')
        .doc(projectID)
        .collection('chapters');

    // Fetch all documents in the first-level subcollection
    final firstLevelSnapshot = await firstLevelSubcollectionRef.get();

    for (var firstLevelDoc in firstLevelSnapshot.docs) {
      // For each document, reference its second-level subcollection
      final secondLevelSubcollectionRef = firstLevelSubcollectionRef
          .doc(firstLevelDoc.id)
          .collection('subsections');

      // Fetch all documents in the second-level subcollection
      final secondLevelSnapshot = await secondLevelSubcollectionRef.get();

      // Delete each document in the second-level subcollection
      for (var secondLevelDoc in secondLevelSnapshot.docs) {
        await secondLevelDoc.reference.delete();
      }

      // After deleting all documents in the second-level subcollection,
      // delete the first-level document
      await firstLevelDoc.reference.delete();

      //------------------------------------------------------------------------

      // do the same for e-publishing
      final firstLevelSubcollectionRefEPublishing = FirebaseFirestore.instance
          .collection('projects')
          .doc(projectID)
          .collection('epublishing');

      // Fetch all documents in the first-level subcollection
      final firstLevelSnapshotEPublishing =
          await firstLevelSubcollectionRefEPublishing.get();

      for (var firstLevelDoc in firstLevelSnapshotEPublishing.docs) {
        // For each document, reference its second-level subcollection
        final secondLevelSubcollectionRef =
            firstLevelSubcollectionRefEPublishing
                .doc(firstLevelDoc.id)
                .collection('subsections');

        // Fetch all documents in the second-level subcollection
        final secondLevelSnapshot = await secondLevelSubcollectionRef.get();

        // Delete each document in the second-level subcollection
        for (var secondLevelDoc in secondLevelSnapshot.docs) {
          await secondLevelDoc.reference.delete();
        }

        // After deleting all documents in the second-level subcollection,
        // delete the first-level document
        await firstLevelDoc.reference.delete();
      }
    }

    // delete questionnaire collection
    final questionnaireRef = FirebaseFirestore.instance
        .collection('projects')
        .doc(projectID)
        .collection('questionnaire');

    questionnaireRef.get().then((value) {
      value.docs.forEach((element) {
        element.reference.delete();
      });
    });

    // Finally, delete the parent document
    await FirebaseFirestore.instance
        .collection('projects')
        .doc(projectID)
        .delete();
  }

  // update project count for user
  Future<void> updateProjectCount(WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    final docRef = db.collection('users').doc(uid);

    await docRef.update({
      'Project Count': await fetch.fetchProjectCount(),
    }).catchError((error) {
      print('Error updating project count: $error');
    });
    ref.refresh(projectCountProvider);
  }

  // delete files from firebase storage
  Future<void> deleteFilesFromFirebaseStorage(List<String> fileUrls) async {
    final FirebaseStorage storage = FirebaseStorage.instance;

    for (String url in fileUrls) {
      try {
        Reference ref = storage.refFromURL(url);
        await ref.delete();
      } catch (e) {
        print('Error deleting file: $url, error: $e');
      }
    }
  }

  Future<void> deleteDiscussionsAndComments(String projectID) async {
    // Start by fetching all discussions with the given projectID
    var discussionsQuery =
        db.collection('discussions').where('projectID', isEqualTo: projectID);
    var discussionsSnapshot = await discussionsQuery.get();

    // Iterate through each discussion document
    for (var discussionDoc in discussionsSnapshot.docs) {
      // Fetch all top-level comments for the discussion
      var commentsQuery = db
          .collection('discussions')
          .doc(discussionDoc.id)
          .collection('comments')
          .where('projectID', isEqualTo: projectID);
      var commentsSnapshot = await commentsQuery.get();

      // Iterate through each comment document
      for (var commentDoc in commentsSnapshot.docs) {
        // Fetch all nested comments for each comment
        var nestedCommentsQuery = db
            .collection('discussions')
            .doc(discussionDoc.id)
            .collection('comments')
            .doc(commentDoc.id)
            .collection('nestedComments')
            .where('projectID', isEqualTo: projectID);
        var nestedCommentsSnapshot = await nestedCommentsQuery.get();

        // Delete each nested comment
        for (var nestedCommentDoc in nestedCommentsSnapshot.docs) {
          await nestedCommentDoc.reference.delete().catchError((error) {
            print('Error deleting nested comment: $error');
          });
        }

        // After deleting nested comments, delete the comment itself
        await commentDoc.reference.delete().catchError((error) {
          print('Error deleting comment: $error');
        });
      }

      // After deleting all comments, delete the discussion itself
      await discussionDoc.reference.delete().catchError((error) {
        print('Error deleting discussion: $error');
      });
    }
  }

  Future<void> deleteFileComments(String projectID) async {
    // Fetch top-level comments associated with the projectID
    final topLevelCommentsQuery =
        db.collection('fileComments').where('projectID', isEqualTo: projectID);
    final topLevelCommentsSnapshot = await topLevelCommentsQuery.get();

    // Iterate over each top-level comment
    for (final topLevelCommentDoc in topLevelCommentsSnapshot.docs) {
      // For each top-level comment, fetch and delete all nested comments
      final nestedCommentsQuery = db
          .collection('fileComments')
          .doc(topLevelCommentDoc.id)
          .collection('nestedComments')
          .where('projectID', isEqualTo: projectID);
      final nestedCommentsSnapshot = await nestedCommentsQuery.get();

      // Delete each nested comment
      for (final nestedCommentDoc in nestedCommentsSnapshot.docs) {
        await nestedCommentDoc.reference.delete().catchError((error) {
          print('Error deleting nested comment: $error');
        });
      }

      // After deleting all nested comments, delete the top-level comment itself
      await topLevelCommentDoc.reference.delete().catchError((error) {
        print('Error deleting top-level comment: $error');
      });
    }
  }

  Future<void> deleteNotificationsByProjectID(String projectID) async {
    // Fetch all notifications for the given userID
    final notificationsQuery =
        db.collection('notifications').where('projectID', isEqualTo: projectID);
    final notificationsSnapshot = await notificationsQuery.get();

    // Delete each notification document
    for (final notificationDoc in notificationsSnapshot.docs) {
      await notificationDoc.reference.delete().catchError((error) {
        print('Error deleting notification: $error');
      });
    }
  }

  // delete project thumbnail
  Future<void> deleteProjectThumbnail(String projectID) async {
    final docRef = db.collection('projects').doc(projectID);

    final DocumentSnapshot documentSnapshot = await docRef.get();

    if (documentSnapshot.exists) {
      Map<String, dynamic> data =
          documentSnapshot.data() as Map<String, dynamic>;
      String? thumbnailUrl = data['thumbnail'];

      if (thumbnailUrl != null &&
          thumbnailUrl.isNotEmpty &&
          thumbnailUrl != 'default') {
        final FirebaseStorage storage = FirebaseStorage.instance;
        try {
          Reference ref = storage.refFromURL(thumbnailUrl);
          await ref.delete();
        } catch (e) {
          print('Error deleting thumbnail: $e');
        }
      } else {
        print('No valid thumbnail URL to delete.');
      }
    } else {
      print('Document does not exist.');
    }
  }

  Future<void> deleteLikesByProjectID(WidgetRef ref, String projectID) async {
    //final String projectID = ref.watch(currentProjectID);

    final List<String> collections = [
      'dislikedDiscussionComments',
      'dislikedDiscussions',
      'dislikedFileComments',
      'dislikedNestedDiscussionComments',
      'dislikedNestedFileComments',
      'likedDiscussionComments',
      'likedDiscussions',
      'likedFileComments',
      'likedNestedDiscussionComments',
      'likedNestedFileComments',
    ];

    try {
      for (String collectionName in collections) {
        QuerySnapshot querySnapshot = await db
            .collection(collectionName)
            .where('projectID', isEqualTo: projectID)
            .get();

        for (QueryDocumentSnapshot docSnapshot in querySnapshot.docs) {
          await db.collection(collectionName).doc(docSnapshot.id).delete();
          print("Deleted document ${docSnapshot.id} from $collectionName");
        }
      }

      print("Deletion of documents with projectID: $projectID is complete.");
    } catch (e) {
      print("Error deleting documents: $e");
    }
  }
}
