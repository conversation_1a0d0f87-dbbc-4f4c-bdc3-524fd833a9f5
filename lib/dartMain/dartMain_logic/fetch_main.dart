import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FetchMain {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<List<String>> getAllProjectThumbnailUrlsByCreator() async {
    String? uid = FirebaseAuth.instance.currentUser?.uid;

    List<String> thumbnailUrls = [];

    QuerySnapshot snapshot = await db.collection("projects").get();

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      if (data.containsKey('thumbnail') && data['uid'] == uid) {
        String title = data['thumbnail'];
        thumbnailUrls.add(title);
      } else {
        //print("Document ${doc.id} does not contain a 'thumbnail' field.");
      }
    }
    return thumbnailUrls;
  }

  Future<List<String>> getAllProjectThumbnailUrlsByPubliclyVisible() async {
    //String? uid = FirebaseAuth.instance.currentUser?.uid;

    List<String> thumbnailUrls = [];

    QuerySnapshot snapshot = await db.collection("projects").get();

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      if (data.containsKey('thumbnail') && data['Visibility'] == 'public') {
        String title = data['thumbnail'];
        thumbnailUrls.add(title);
      } else {
        //print("Document ${doc.id} does not contain a 'thumbnail' field.");
      }
    }
    return thumbnailUrls;
  }

  Future<List<String>> fetchProjectIDsByOwner(String userID) async {
    List<String> projectIDs = [];
    try {
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection("projects")
          .where("uid", isEqualTo: userID)
          .get();
      for (QueryDocumentSnapshot documentSnapshot in snapshot.docs) {
        projectIDs.add(documentSnapshot.id);
      }
    } catch (e) {
      print('Error fetching project IDs: $e');
    }
    return projectIDs;
  }

  Future<bool> fetchNotificationStatus() async {
    try {
      String? uid = FirebaseAuth.instance.currentUser?.uid;

      DocumentSnapshot snapshot =
          await FirebaseFirestore.instance.collection("users").doc(uid).get();

      if (snapshot.exists) {
        // Cast snapshot data to a map and access the 'Notification' field
        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
        return data['Notifications'] ?? false;
      }
    } catch (e) {
      print('Error fetching notification status: $e');
    }
    return false;
  }
}
