import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ProjectsCreateLogicFetch {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  // gets the key-value value from "Project Name" within ProjectID doc in /projects
  Future<List<String>> fetchAllProjectNamesByUser(String keyToRetrieve) async {
    List<String> values = [];
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection("projects")
          .where("uid", isEqualTo: uid)
          .get();

      for (QueryDocumentSnapshot documentSnapshot in querySnapshot.docs) {
        final data = documentSnapshot.data() as Map<String, dynamic>;
        if (data.containsKey("Project Name")) {
          // Check if the document contains the desired key
          String value = documentSnapshot[keyToRetrieve];
          values.add(value);
        }
      }
    } catch (e) {
      // Handle any errors that may occur during the data retrieval.
      print('Error: $e');
    }
    return values;
  }

// gets the document names within users/projects
  Future<List<String>> fetchAllProjectIDsByUser() async {
    List<String> documentNames = [];

    final User? user = auth.currentUser;
    final uid = user?.uid;

    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection("projects")
          .where("uid", isEqualTo: uid)
          .get();

      for (QueryDocumentSnapshot documentSnapshot in querySnapshot.docs) {
        documentNames.add(documentSnapshot.id);
      }
    } catch (e) {
      // Handle any errors that may occur during the data retrieval.
      print('Error: $e');
    }

    return documentNames;
  }

  Future<List<String>> fetchAllProjectVisibleIDsByUser() async {
    List<String> visibleProjectIDs = [];

    final User? user = auth.currentUser;
    final uid = user?.uid;

    if (uid == null) {
      print("No current user found");
      return visibleProjectIDs; // Return empty if no user is logged in
    }

    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection("projects")
          .where("uid", isEqualTo: uid)
          .get();

      for (QueryDocumentSnapshot documentSnapshot in querySnapshot.docs) {
        final data = documentSnapshot.data() as Map<String, dynamic>;
        if (data.containsKey("Visible Project ID")) {
          // Extract the 'Visible Project ID' if it exists
          String visibleId = data["Visible Project ID"];
          visibleProjectIDs.add(visibleId);
        }
      }
    } catch (e) {
      // Handle any errors that may occur during the data retrieval.
      print('Error fetching project IDs: $e');
    }

    return visibleProjectIDs;
  }
}
