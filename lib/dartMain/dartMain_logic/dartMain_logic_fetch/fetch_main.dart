import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FetchMain {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<List<String>> fetchAllProjectThumbnailUrlsByCreator() async {
    String? uid = FirebaseAuth.instance.currentUser?.uid;

    List<String> thumbnailUrls = [];

    QuerySnapshot snapshot = await db.collection("projects").get();

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      if (data.containsKey('thumbnail') && data['uid'] == uid) {
        String title = data['thumbnail'];
        thumbnailUrls.add(title);
      } else {
        //print("Document ${doc.id} does not contain a 'thumbnail' field.");
      }
    }
    return thumbnailUrls;
  }
}
