import 'package:web_app/main.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';

// initializes certain properties on login
class OnLogin {
  FetchDatabase fetch = FetchDatabase();
  void initializeProvidersOnLogin(WidgetRef ref) async {
    // ref.read(projectCountProvider.notifier).state =
    //     await fetch.getProjectCount();

    // read of this provider called at updateDisplayName(in writeDatabaseMetadata.dart)
    // watch called within navDrawerReader.dart and nawDrawer.dart
    ref.read(currentUserFirstLetterProvider.notifier).state =
        await chopUsername();

    ref.read(planTypeProvider.notifier).state = await fetch.fetchPlanType();
    ref.read(displayNameProvider.notifier).state =
        await fetch.fetchDisplayName();
  }

  Future<String> chopUsername() async {
    try {
      String username = await fetch.fetchDisplayName();
      if (username.isNotEmpty) {
        return username[
            0]; // Assuming you want the first character of the username
      } else {
        return 'U'; // Default value if username is empty
      }
    } catch (e) {
      // Handle the error, possibly logging it or showing a message
      print('Error fetching username: $e');
      return 'U'; // Default or error value
    }
  }
}
