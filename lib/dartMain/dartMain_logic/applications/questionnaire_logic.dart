import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/main.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'package:web_app/readerMode/main_logic/marketplace_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/ordered_item_logic.dart';

class QuestionnaireLogic {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  MarketPlaceLogic logic = MarketPlaceLogic();

  Future<List<OrderedItem>> fetchQuestionnaireData(WidgetRef ref) async {
    DocumentReference docRef = db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .collection('questionnaire')
        .doc('questions');

    DocumentSnapshot snapshot = await docRef.get();

    if (snapshot.exists && snapshot.data() != null) {
      Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
      List<dynamic> questionsData = data['questions'] as List<dynamic>;
      return questionsData
          .map((q) => OrderedItem.fromMap(q as Map<String, dynamic>))
          .toList();
    }
    return []; // Return an empty list if there's no data or the data is malformed
  }

  Future<int> fetchNumberOfQuestions(String projectID) async {
    int dataLength = 0;

    try {
      DocumentSnapshot docSnapshot = await db
          .collection("projects")
          .doc(projectID)
          .collection('questionnaire')
          .doc('questions')
          .get();

      if (docSnapshot.exists) {
        var data = docSnapshot.data() as Map<String, dynamic>?;
        if (data != null && data.containsKey('items')) {
          List<dynamic> itemsData = data['items'];
          List<OrderedItem> items = itemsData.map((itemData) {
            return OrderedItem.fromMap(itemData as Map<String, dynamic>);
          }).toList();
          dataLength = items.length;
        }
      }
    } catch (e) {
      print("Error fetching number of questions: $e");
    }

    return dataLength;
  }

  Future<void> addQuestionnaireData(WidgetRef ref, String newQuestion) async {
    DocumentReference docRef = db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .collection('questionnaire')
        .doc('questions');
    DocumentSnapshot snapshot = await docRef.get();

    // Cast snapshot.data() to Map<String, dynamic>
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    List<OrderedItem> questions = (data['questions'] as List<dynamic>)
        .map((q) => OrderedItem.fromMap(q as Map<String, dynamic>))
        .toList();

    // Find the highest order and add 1
    int newOrder = questions.isEmpty ? 1 : questions.last.order + 1;
    questions.add(OrderedItem(newOrder, newQuestion));

    await docRef.set({'questions': questions.map((q) => q.toMap()).toList()});
    ref.refresh(numberOfQuestionsByProjectProvider);
  }

  Future<String> fetchLastQuestionnaireKey(WidgetRef ref) async {
    String lastKey = '';
    // Fetch data from the database
    lastKey = await db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .collection('questionnaire')
        .doc('questions')
        .get()
        .then((value) => value.data()!.keys.last);

    return lastKey;
  }

  Future<void> updateQuestionnaireData(
      WidgetRef ref, int order, String updatedQuestion) async {
    DocumentReference docRef = db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .collection('questionnaire')
        .doc('questions');
    DocumentSnapshot snapshot = await docRef.get();

    // Cast snapshot.data() to Map<String, dynamic>
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    List<OrderedItem> questions = (data['questions'] as List<dynamic>)
        .map((q) => OrderedItem.fromMap(q as Map<String, dynamic>))
        .toList();

    questions.firstWhere((item) => item.order == order).question =
        updatedQuestion;

    await docRef.set({'questions': questions.map((q) => q.toMap()).toList()});
  }

  Future<void> deleteQuestionnaireData(WidgetRef ref, int order) async {
    DocumentReference docRef = db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .collection('questionnaire')
        .doc('questions');
    DocumentSnapshot snapshot = await docRef.get();

    // Cast snapshot.data() to Map<String, dynamic>
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    List<OrderedItem> questions = (data['questions'] as List<dynamic>)
        .map((q) => OrderedItem.fromMap(q as Map<String, dynamic>))
        .toList();

    questions.removeWhere((item) => item.order == order);

    await docRef.set({'questions': questions.map((q) => q.toMap()).toList()});
    ref.refresh(numberOfQuestionsByProjectProvider);
  }

  // Future<List<String>> fetchProjectsWithApplicationsByUser() async {
  //   final User? user = auth.currentUser;
  //   final uid = user?.uid;
  //   List<String> projectIDs = [];

  //   // Fetch data from the database
  //   await db
  //       .collection("applications")
  //       .where('projectOwner', isEqualTo: uid)
  //       .get()
  //       .then((value) => value.docs.forEach((element) {
  //             projectIDs.add(element['projectName']);
  //           }));

  //   return projectIDs;
  // }

  // uses a set to avoid duplicates
  Future<List<String>> fetchProjectsWithApplicationsByUser() async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    Set<String> projectIDs = {}; // Use a Set to avoid duplicates

    // Fetch data from the database
    await db
        .collection("applications")
        .where('projectOwner', isEqualTo: uid)
        .get()
        .then((value) => value.docs.forEach((element) {
              projectIDs.add(element['projectName']);
            }));

    return projectIDs
        .toList(); // Convert the Set back to a List before returning
  }

  Future<List<String>> fetchProjectIDsWithApplicationsByUser() async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    List<String> projectIDs = [];

    // Fetch data from the database
    await db
        .collection("applications")
        .where('projectOwner', isEqualTo: uid)
        .get()
        .then((value) => value.docs.forEach((element) {
              projectIDs.add(element['projectID']);
            }));

    return projectIDs;
  }

  Future<List<String>> fetchApplicationStatusByUser() async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    List<String> projectIDs = [];

    // Fetch data from the database
    await db
        .collection("applications")
        .where('uid', isEqualTo: uid)
        .get()
        .then((value) => value.docs.forEach((element) {
              projectIDs.add(element['projectID']);
            }));

    return projectIDs;
  }

  // Fetch all the applicants for a specific project
  Future<List<List<String>>> fetchApplicantsByProject(String projectID) async {
    List<List<String>> applications = [];
    final DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm');

    // Fetch data from the Firestore database
    await FirebaseFirestore.instance
        .collection("applications")
        .where('projectID', isEqualTo: projectID)
        .get()
        .then((value) => value.docs.forEach((element) {
              // Create a sublist for each application and add it to the applications list
              String displayName = element.data()['DisplayName'];
              Timestamp timestamp = element.data()['timeStamp'];
              DateTime dateTime = timestamp.toDate();
              String formattedDate = formatter.format(dateTime);

              List<String> applicantDetails = [
                displayName,
                formattedDate,
              ];
              applications.add(applicantDetails);
            }));

    return applications;
  }

  Future<String> fetchApplicantionID(String projectID) async {
    String applicationID = "";

    await FirebaseFirestore.instance
        .collection("applications")
        .where('projectID', isEqualTo: projectID)
        .get()
        .then((value) => value.docs.forEach((element) {
              applicationID = element.data()['applicationID'];
            }));

    return applicationID;
  }

  Future<String> fetchUID(String applicationID) async {
    String uid = "";

    await FirebaseFirestore.instance
        .collection("applications")
        .where('applicationID', isEqualTo: applicationID)
        .get()
        .then((value) => value.docs.forEach((element) {
              uid = element.data()['uid'];
            }));

    return uid;
  }

  // this is basically the same method as in marketplace_logic
  // the difference being the uid is dynamic here
  // which is necessary since we're not dealing with the signed-in user
  Future<void> subscribeUserToProject(String uid, WidgetRef ref) async {
    if (uid == null) return;

    final docRef = db.collection('projectSubscriptions').doc(uid);
    final docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      // Document does not exist, so we use set to create it initially.
      await docRef.set({ref.watch(currentProjectID): true});
    } else {
      // Document exists, append or update the subscriptions map
      await docRef.update({ref.watch(currentProjectID): true});
    }
  }

  Future<List<OrderedItem>> fetchApplicantResponsesByID(
      String applicationID) async {
    List<OrderedItem> responses = [];

    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection("applications")
          .where('applicationID', isEqualTo: applicationID)
          .get();

      for (var doc in querySnapshot.docs) {
        var data = doc.data() as Map<String, dynamic>?;
        if (data != null && data.containsKey('items')) {
          List<dynamic> itemsData = data['items'];
          List<OrderedItem> items = itemsData.map((itemData) {
            return OrderedItem.fromMap(itemData as Map<String, dynamic>);
          }).toList();
          responses.addAll(items);
        }
      }

      print("Fetched responses: $responses");
    } catch (e) {
      print("Error fetching responses: $e");
    }

    return responses;
  }

  Future<void> denyUserApplication(String applicationID, WidgetRef ref) async {
    // delete application
    await db.collection("applications").doc(applicationID).delete();
    ref.refresh(numberOfApplicationsByProject);
    ref.refresh(numberOfProjectsWithApplications);
  }
}
