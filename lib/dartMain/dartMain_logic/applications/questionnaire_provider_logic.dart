import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';

class QuestionnaireProviderLogic {
  QuestionnaireLogic logic = QuestionnaireLogic();
  Future<int> fetchNumberOfApplicationsbyProject(String projectID) async {
    List<List<String>> applicationsList =
        await logic.fetchApplicantsByProject(projectID);

    int length = applicationsList.length;
    return length;
  }

  Future<int> fetchNumberOfProjectsWithApplications() async {
    List<String> projectIDs =
        await logic.fetchProjectIDsWithApplicationsByUser();

    int length = projectIDs.length;
    return length;
  }

  Future<bool> hasAppliedToProject(String uid, String projectID) async {
    List<String> projectIDs = await logic.fetchApplicationStatusByUser();
    return projectIDs.contains(projectID);
  }
}
