// import 'package:cloud_firestore/cloud_firestore.dart';

// class OrderedItem {
//   int order;
//   String question;
//   String? answer;

//   OrderedItem(this.order, this.question, [this.answer]);

//   Map<String, dynamic> toMap() {
//     return {
//       'order': order,
//       'question': question,
//       'answer': answer ??
//           FieldValue
//               .delete(), // Use FieldValue.delete() to handle null answers when updating Firestore.
//     };
//   }

//   static OrderedItem fromMap(Map<String, dynamic> map) {
//     return OrderedItem(
//       map['order'] as int,
//       map['question'] as String,
//       map['answer'] as String?,
//     );
//   }
// }

class OrderedItem {
  int order;
  String question;
  String? answer;

  OrderedItem(this.order, this.question, [this.answer]);

  Map<String, dynamic> toMap() {
    return {
      'order': order,
      'question': question,
      if (answer != null)
        'answer': answer, // Only include answer if it's not null
    };
  }

  // Factory constructor to create an OrderedItem from a Map
  factory OrderedItem.fromMap(Map<String, dynamic> map) {
    return OrderedItem(
      map['order'] as int,
      map['question'] as String,
      map['answer'] as String?, // This can be null, hence the explicit cast
    );
  }
}
