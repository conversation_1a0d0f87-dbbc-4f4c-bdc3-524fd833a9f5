import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/ordered_item_logic.dart';
import 'dart:math';

class ProjectsCreateLogic {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;
  FetchDatabase fetch = FetchDatabase();

  Future<String> createProjectID(WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    // this variable never decreases, it is a total regardless of project deletion
    int numberOfDocuments = await fetch.fetchNumProjectsCreated() + 1;

    ref.refresh(projectCountProvider);

    String projectID = "proj-" + uid! + "-" + numberOfDocuments.toString();

    return projectID;
  }

  Future<void> addProjectData(String projectName, String projectDescription,
      String projectVisibility, WidgetRef ref) async {
    createProjectID(ref);
    final User? user = auth.currentUser;
    final uid = user?.uid;

    String projectId = await createProjectID(ref);

    await db.collection("projects").doc(projectId).set({
      'Project Name': projectName,
      'Description': projectDescription,
      'Layout': 'chapters',
      'Visibility': projectVisibility,
      'uid': uid,
      'thumbnail': 'default',
      'thumbnail name': 'default',
      'Project ID': projectId,
      'tags': ['new'],
      //'Display Name': user!.displayName ?? 'Anonymous',
      'Display Name': ref.watch(displayNameProvider) ?? 'Anonymous',
      'timestamp': FieldValue.serverTimestamp(),
      'Subscribers': 1,
      'Storage Used': 0,
      'Visible Project ID': generateRandomString(15),
    });

    // all new projects get one chapter regardless of layout selection
    await db
        .collection("projects")
        .doc(projectId)
        .collection("chapters")
        .doc("chapter_1")
        .set({"title": "Chapter 1"});

    await db
        .collection("projects")
        .doc(projectId)
        .collection("chapters")
        .doc("chapter_1")
        .collection("subsections")
        .doc("subsection_1")
        .set({"title": "Subsection 1"});

    // all new projects get one e-publishgin section regardless of layout selection
    await db
        .collection("projects")
        .doc(projectId)
        .collection("epublishing")
        .doc("epchapter_1")
        .set({"title": "Chapter 1"});

    await db
        .collection("projects")
        .doc(projectId)
        .collection("epublishing")
        .doc("epchapter_1")
        .collection("subsections")
        .doc("epsubsection_1")
        .set({"title": "Subsection 1"});
    await db
        .collection('users')
        .doc(auth.currentUser!.uid)
        .update({'Project Count': await fetch.fetchProjectCount()});

    //--------------------------------------------------------------------------------

    await db.collection('users').doc(auth.currentUser!.uid).update(
        {'Projects Created': await fetch.fetchNumProjectsCreated() + 1});

    // Adding section for private projects
    if (projectVisibility.toLowerCase() == 'private') {
      List<Map<String, dynamic>> initialQuestions = [
        OrderedItem(1, 'My first question').toMap()
      ];
      try {
        await db
            .collection('projects')
            .doc(projectId)
            .collection('questionnaire')
            .doc('questions')
            .set({'questions': initialQuestions});
      } catch (e) {
        print('Failed to add initial question: $e');
      }
    }

    if (uid == null) return;

    final docRef = db.collection('projectSubscriptions').doc(uid);
    final docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      // Document does not exist, so we use set to create it initially.
      await docRef.set({projectId: true});
    } else {
      // Document exists, append or update the subscriptions map
      await docRef.update({projectId: true});
    }
  }

  String generateRandomString(int length) {
    const String _chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    Random _rnd = Random();

    return String.fromCharCodes(Iterable.generate(
        length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));
  }
}
