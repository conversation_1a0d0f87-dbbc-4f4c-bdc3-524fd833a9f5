import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/projects_create_logic.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CreateNewUser {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;
  ProjectsCreateLogic obj = ProjectsCreateLogic();

  // creates a new user
  Future<void> addUserData(User user, WidgetRef ref) async {
    try {
      final uid = user.uid;
      await db.collection("users").doc(uid).set({
        'uid': uid,
        'Account Type': 'BookBranch',
        "Display Name": user.email,
        "Project Count": 0,
        // just added
        "Projects Created": 0,
        "Notifications": true,
        'Storage Used': 0,
        'Storage Limit': ************,
        'emailVerified': false, // Add email verification status
      });

      print("User data added successfully");
    } catch (e) {
      print("Failed to add user data: $e");
    }
  }

  Future<void> addUserDataPlus(User user, WidgetRef ref) async {
    try {
      final uid = user.uid;
      await db.collection("users").doc(uid).set({
        'uid': uid,
        'Account Type': 'BookBranch+',
        "Display Name": user.email,
        "Project Count": 0,
        // just added
        "Projects Created": 0,
        "Notifications": true,
        'Storage Used': 0,
        'Storage Limit': ************,
        'emailVerified': false, // Add email verification status
      });

      print("User data added successfully");
    } catch (e) {
      print("Failed to add user data: $e");
    }
  }
}
