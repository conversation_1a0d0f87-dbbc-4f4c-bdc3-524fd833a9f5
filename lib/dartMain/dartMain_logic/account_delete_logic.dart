import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_logic/fetch_main.dart';
import 'package:web_app/dartMain/dartMain_logic/project_delete_logic.dart';
import 'package:web_app/readerMode/main_logic/marketplace_logic.dart';

// this class works in tandem with ProjectDeleteLogic
// to delete all user data and then the user account
// deleteAllProjectsForUser() runs according to ProjectDeleteLogic except for making use of
// a different method to delete all project subscriptions

class AccountDeleteLogic {
  MarketPlaceLogic marketPlaceLogic = MarketPlaceLogic();
  Future<void> deleteAllProjectsForUser(String userID, WidgetRef ref) async {
    print("Starting deletion of all projects for user: $userID");
    FetchMain fetchMain = FetchMain();

    List<String> projectIDs = await fetchMain.fetchProjectIDsByOwner(userID);
    print("Fetched project IDs for user: $projectIDs");
    ProjectsDeleteLogic deleteLogic = ProjectsDeleteLogic();

    for (String projectID in projectIDs) {
      try {
        print("Deleting project: $projectID");
        // Delete files from Firebase Storage
        List<String> fileUrls =
            await deleteLogic.deleteDocumentsMatchingProjectID(projectID);
        print("Deleting files from Firebase Storage: $fileUrls");
        await deleteLogic.deleteFilesFromFirebaseStorage(fileUrls);

        // start by deleting all pending applications for the project
        deleteLogic.deletePendingApplicationsForProject(projectID);

        // Delete project thumbnail
        await deleteLogic.deleteProjectThumbnail(projectID);
        print("Deleted project thumbnail for project: $projectID");

        // Delete user notifications
        await deleteNotificationsByUserID(userID);

        // Delete discussions and comments
        await deleteLogic.deleteDiscussionsAndComments(projectID);
        print("Deleted discussions and comments for project: $projectID");

        // Delete file comments
        await deleteLogic.deleteFileComments(projectID);
        print("Deleted file comments for project: $projectID");

        // Unsubscribe all users from the project
        await unsubscribeDeleteAccount(projectID);
        print("Unsubscribed all users from project: $projectID");

        // Delete likes and dislikes related to the project
        await deleteLogic.deleteLikesByProjectID(ref, projectID);
        print("Deleted likes and dislikes for project: $projectID");

        // Delete the project itself
        await deleteLogic.deleteProject(projectID, ref);
        print("Deleted project: $projectID");
      } catch (e) {
        print("Error deleting project $projectID: $e");
      }
    }

    // Update project count for the user
    await deleteLogic.updateProjectCount(ref);
    print("Updated project count for the user.");
  }

//   // this should be placed here not in project_delete_logic obviously
  Future<void> deleteNotificationsByUserID(String userID) async {
    // Fetch all notifications for the given userID
    final notificationsQuery =
        db.collection('notifications').where('userID', isEqualTo: userID);
    final notificationsSnapshot = await notificationsQuery.get();

    // Delete each notification document
    for (final notificationDoc in notificationsSnapshot.docs) {
      await notificationDoc.reference.delete().catchError((error) {
        print('Error deleting notification: $error');
      });
    }
  }

  FirebaseFirestore db = FirebaseFirestore.instance;
//   final FirebaseAuth auth = FirebaseAuth.instance;

// // this method is used to unsubscribe all users from a project and delete the document
  Future<void> unsubscribeDeleteAccount(String projectId) async {
    // Reference to the collection
    final collectionRef = db.collection('projectSubscriptions');
    // Fetch all documents; caution with large collections
    final querySnapshot = await collectionRef.get();

    // Prepare a batch write to handle multiple document deletions
    WriteBatch batch = db.batch();

    for (var doc in querySnapshot.docs) {
      if (doc.data().containsKey(projectId)) {
        // Check if the document contains the project ID
        final docRef = db.collection('projectSubscriptions').doc(doc.id);
        batch.delete(docRef); // Delete the entire document
      }
    }

    // Commit the batch
    await batch.commit().catchError((error) {
      print('Error unsubscribing and deleting project subscriptions: $error');
    });
  }
}
