import 'package:cloud_firestore/cloud_firestore.dart';

class UserMetadataFetch {
  final String accountType;
  final String displayName;
  final int projectCount;
  final String uid;

  UserMetadataFetch({
    required this.accountType,
    required this.displayName,
    required this.projectCount,
    required this.uid,
  });

  factory UserMetadataFetch.fromFireStore(DocumentSnapshot doc) {
    Map data = doc.data() as Map<String, dynamic>;
    return UserMetadataFetch(
      accountType: data['Account Type'] ?? '',
      displayName: data['Display Name'] ?? '',
      projectCount: data['Project Count'] ?? '',
      uid: data['uid'] ?? '',
    );
  }

  static Future<UserMetadataFetch?> fetchUserMetaData(String uid) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    DocumentSnapshot doc = await firestore.collection('users').doc(uid).get();

    if (doc.exists) {
      return UserMetadataFetch.fromFireStore(doc);
    }
    return null;
  }

  dynamic getPropertyValue(String propertyName) {
    switch (propertyName) {
      case 'Account Type':
        return accountType;
      case 'Display Name':
        return displayName;
      case 'Project Count':
        return projectCount;
      case 'uid':
        return uid;
      default:
        return null; // Or throw an exception
    }
  }
}
