import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';

class ProjectSettingsLogic {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;
  Future<void> updateProjectThumbnail(
      WidgetRef ref, String newThumbnail, String thumbnailName) async {
    await db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .update({'thumbnail': newThumbnail});

    await db
        .collection("projects")
        .doc(ref.watch(currentProjectID))
        .update({'thumbnail name': thumbnailName});
  }

  Future<void> uploadProjectTagToFirestore(WidgetRef ref, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('projects');

    try {
      final querySnapshot = await dataCollection
          .where('Project ID', isEqualTo: ref.watch(currentProjectID))
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final docId = querySnapshot.docs.first.id;

        await dataCollection.doc(docId).update({
          'tags': FieldValue.arrayUnion([tag]),
        });
        print("Document updated successfully.");
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> removeProjectTagToFirestore(WidgetRef ref, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('projects');

    try {
      final querySnapshot = await dataCollection
          .where('Project ID', isEqualTo: ref.watch(currentProjectID))
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final docId = querySnapshot.docs.first.id;

        await dataCollection.doc(docId).update({
          'tags': FieldValue.arrayRemove([tag]),
        });
        print("Document updated successfully.");
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }
}
