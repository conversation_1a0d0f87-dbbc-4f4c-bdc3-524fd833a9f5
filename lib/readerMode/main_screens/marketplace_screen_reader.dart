import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/main_widgets/marketplace_widget_reader.dart';
import 'package:flutter/material.dart';

class MarketPlaceScreenReader extends ConsumerWidget {
  static const String routeName = '/marketPlaceScreenReader';

  // TODO: implement build
  //@override
  Widget build(BuildContext context, WidgetRef ref) {
    // Wrap MarketPlaceWidgetReader with Scaffold to include AppBar and NavDrawer
    return MarketPlaceWidgetReader();
  }
}
