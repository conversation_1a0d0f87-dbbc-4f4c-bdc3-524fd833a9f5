import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_widgets/account_details_widget.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class AccountDetailsReaderScreen extends ConsumerWidget {
  static const String routeName = '/accountDetailsReaderScreen';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Account Settings',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: const AccountDetailsWidget(),
    );
  }
}
