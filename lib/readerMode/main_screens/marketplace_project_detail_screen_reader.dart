import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/main_widgets/marketplace_project_detail_widget_reader.dart';

class MarketPlaceProjectDetailScreenReader extends ConsumerWidget {
  static const String routeName = '/marketPlaceProjectDetailScreenReader';

  // TODO: implement build
  //@override
  Widget build(BuildContext context, WidgetRef ref) {
    // Wrap MarketPlaceWidgetReader with Scaffold to include AppBar and NavDrawer
    return MarketPlaceProjectDetailWidgetReader();
  }
}
