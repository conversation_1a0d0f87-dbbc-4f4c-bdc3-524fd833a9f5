import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/main_widgets/subscriptions_widget_reader.dart';

class SubscriptionsScreenReader extends ConsumerWidget {
  static const String routeName = '/subscriptionsScreenReader';

  const SubscriptionsScreenReader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SubscriptionsWidgetReader();
  }
}
