import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/write_database_subsections.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/readerMode/database_screens/epublishing_files_screen_reader.dart';

class EPublishingSubsectionsWidgetReader extends ConsumerStatefulWidget {
  @override
  _EPublishingSubsectionsWidgetState createState() =>
      _EPublishingSubsectionsWidgetState();
  final WriteDatabaseSubsections databaseObjSubsections =
      WriteDatabaseSubsections();
}

class _EPublishingSubsectionsWidgetState
    extends ConsumerState<EPublishingSubsectionsWidgetReader> {
  @override
  Widget build(BuildContext context) {
    final chapterId = ref.watch(currentEPublishingChapter);

    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('projects')
          .doc(ref.watch(currentProjectID))
          .collection('epublishing')
          .doc(chapterId)
          .collection('subsections')
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData) {
          return const Center(child: Text("No subsections found"));
        }

        return ListView.builder(
          itemCount: snapshot.data!.docs.length,
          itemBuilder: (context, index) {
            DocumentSnapshot subsection = snapshot.data!.docs[index];
            String title = subsection.get('title') ?? 'Untitled Subsection';

            return Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color:
                      const Color.fromARGB(255, 44, 148, 44), // Green outline
                  width: 1.5, // Slightly thicker outline
                ),
              ),
              child: ListTile(
                leading: const Icon(
                  Icons.description_outlined,
                  color: Color.fromARGB(255, 44, 148, 44),
                  size: 20,
                ),
                title: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    letterSpacing: 0.2,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: const Icon(
                  Icons.arrow_forward_ios,
                  color: Color.fromARGB(255, 44, 148, 44),
                  size: 16,
                ),
                onTap: () {
                  ref.read(currentEPublishingSubsection.notifier).state = subsection
                      .id; // Ensure to use subsection.id to correctly identify subsection
                  Navigator.of(context)
                      .push(MaterialPageRoute(builder: (context) {
                    return EPublishingFilesScreenReader(); // Navigate to Files page
                  }));
                },
              ),
            );
          },
        );
      },
    );
  }
}
