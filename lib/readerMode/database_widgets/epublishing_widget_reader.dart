import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/database/database_logic/delete_data.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/readerMode/database_screens/epublishing_subsections_screen_reader.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class EPublishingWidgetReader extends ConsumerStatefulWidget {
  final WriteDatabase databaseObj = WriteDatabase();
  final FetchDatabase fetch = FetchDatabase();

  @override
  _EPublishingWidgetReaderState createState() =>
      _EPublishingWidgetReaderState();
}

class _EPublishingWidgetReaderState
    extends ConsumerState<EPublishingWidgetReader> {
  List<String>? titlesData;
  final DeleteData deleteData = DeleteData();
  static const String routeName = '/databaseMain';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final titlesAsyncValue = ref.watch(titlesProviderEPublishing);
    titlesAsyncValue.when(
      data: (data) {
        setState(() => titlesData = data);
      },
      loading: () {},
      error: (e, st) {},
    );
    final fileMetadata = ref.watch(fileMetadataProviderEPublishingChapterLevel);
    fileMetadata;

    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          ref.watch(currentProjectName),
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: FutureBuilder<List<String>>(
        future: widget.fetch.fetchAllEPublishingChapterTitles(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasError) {
              return Text('Error: ${snapshot.error}');
            } else if (snapshot.hasData) {
              return ListView.builder(
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(20),
                          spreadRadius: 1,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: globals.bookBranchGreen, // Green outline
                        width: 1.5, // Slightly thicker outline
                      ),
                    ),
                    child: InkWell(
                      onTap: () {
                        if (titlesData != null && titlesData!.isNotEmpty) {
                          ref.read(currentEPublishingChapter.notifier).state =
                              titlesData![index];
                          Navigator.of(context)
                              .push(MaterialPageRoute(builder: (context) {
                            return EPublishingSubsectionsScreenReader();
                          }));
                        } else {
                          // No data available
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.menu_book_outlined,
                                    color: globals.bookBranchGreen,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Flexible(
                                    child: Text(
                                      snapshot.data![index],
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                        letterSpacing: 0.2,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: globals.bookBranchGreen.withAlpha(180),
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            } else {
              return const Center(child: Text('No data available'));
            }
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }
}
