import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_widgets/display_video_widget.dart';
import 'package:web_app/readerMode/database_screens/file_comments_screen_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:photo_view/photo_view.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:cloud_firestore/cloud_firestore.dart';

class FileDetailsWidgetReader extends ConsumerWidget {
  final String thumbnailUrl =
      "assets/no_data.png"; // Assuming this is available

  final String fileUrl; // Initialize with the URL of the file
  final String?
      documentId; // Optional document ID for specific file identification
  final String?
      expectedDisplayName; // Expected display name to help identify the correct duplicate
  final String?
      expectedDescription; // Expected description to help identify the correct duplicate
  final Timestamp?
      expectedTimestamp; // Expected timestamp to help identify the correct duplicate

  FileDetailsWidgetReader({
    required this.fileUrl,
    this.documentId,
    this.expectedDisplayName,
    this.expectedDescription,
    this.expectedTimestamp,
  });

  UniversalWidgets universals = UniversalWidgets();
  TextEditingController input = TextEditingController();

  // Helper method to get appropriate icon for file type
  IconData _getFileTypeIcon(String fileType) {
    switch (fileType) {
      case 'Image':
        return Icons.image;
      case 'PDF':
        return Icons.picture_as_pdf;
      case 'Video':
        return Icons.videocam;
      default:
        return Icons.file_present;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        title: Text(
          'File Details',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
      ),
      body: FutureBuilder<FileMetadata?>(
        future: documentId != null
            ? FileMetadata.fetchSingleFileMetadataById(documentId!)
            : (expectedDisplayName != null ||
                    expectedDescription != null ||
                    expectedTimestamp != null)
                ? FileMetadata.fetchSingleFileMetadataByUrlAndIdentifiers(
                    fileUrl,
                    expectedDisplayName: expectedDisplayName,
                    expectedDescription: expectedDescription,
                    expectedTimestamp: expectedTimestamp,
                  )
                : FileMetadata.fetchSingleFileMetadataByUrl(fileUrl),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text("Error loading file details"));
          } else if (snapshot.data == null) {
            return const Center(child: Text("No file details found"));
          } else {
            // Successfully fetched file metadata
            FileMetadata fileMetadata = snapshot.data!;
            return Column(
              children: [
                // Media preview section with file type indicator
                Expanded(
                  flex: 1,
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(15),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        children: [
                          // Media content
                          InkWell(
                            onTap: () {
                              // Navigate to a full-screen view or handle the tap as needed
                              switch (fileMetadata.fileType) {
                                case 'Image':
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => FullScreenMediaView(
                                          fileMetadata.fileURL),
                                    ),
                                  );
                                  break;
                                case 'PDF':
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => MobilePdfViewer(
                                          pdfUrl: fileMetadata.fileURL),
                                    ),
                                  );
                                  break;
                                case 'Video':
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => DisplayVideoWidget(
                                          videoUrl: fileMetadata.fileURL),
                                    ),
                                  );
                                  break;
                                default:
                                  // Handle unknown media type
                                  break;
                              }
                            },
                            child: Container(
                              width: double.infinity,
                              height: double.infinity,
                              child: fileMetadata.fileType == 'Image'
                                  ? Image.network(
                                      fileMetadata.fileURL,
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: double.infinity,
                                    )
                                  : fileMetadata.fileType == 'Video'
                                      ? Image.asset(
                                          'assets/play_image.png', // Placeholder image for videos
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          height: double.infinity,
                                        )
                                      : fileMetadata.fileType == 'PDF'
                                          ? Image.asset(
                                              'assets/pdf_icon.png', // Placeholder image for PDFs
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              height: double.infinity,
                                            )
                                          : Container(
                                              color: Colors.grey.shade200,
                                              child: const Center(
                                                child: Icon(
                                                  Icons.file_present,
                                                  size: 80,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ),
                            ),
                          ),
                          // File type indicator badge
                          Positioned(
                            top: 12,
                            right: 12,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: globals.bookBranchGreen.withAlpha(200),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(25),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                fileMetadata.fileType,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(10),
                            blurRadius: 10,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        border: Border.all(
                          color: bookBranchGreen,
                          width: 1.5,
                        ),
                      ),
                      child: Stack(
                        children: [
                          // Decorative element - top gradient bar
                          Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: 6,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    bookBranchGreen,
                                    bookBranchGreen.withAlpha(178),
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(12),
                                  topRight: Radius.circular(12),
                                ),
                              ),
                            ),
                          ),
                          // Decorative element - corner accent
                          Positioned(
                            top: 6,
                            right: 0,
                            child: Container(
                              height: 24,
                              width: 24,
                              decoration: BoxDecoration(
                                color: bookBranchGreen.withAlpha(25),
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(24),
                                ),
                              ),
                            ),
                          ),
                          // File type icon
                          Positioned(
                            top: 20,
                            left: 16,
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: bookBranchGreen.withAlpha(25),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                _getFileTypeIcon(fileMetadata.fileType),
                                color: bookBranchGreen,
                                size: 24,
                              ),
                            ),
                          ),
                          // Main content
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 75, 16, 16),
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // File name section
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          fileMetadata.displayName,
                                          style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w600,
                                            color: Color.fromARGB(204, 0, 0, 0),
                                            letterSpacing: 0.3,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      // Comments button
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(10),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: universals.buildButtonFlat(
                                          "Comments",
                                          true,
                                          () async {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    FileCommentsScreenReader(),
                                              ),
                                            );
                                          },
                                          bookBranchGreen,
                                          Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  // Description section
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade50,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Row(
                                          children: [
                                            Icon(
                                              Icons.description_outlined,
                                              color: bookBranchGreen,
                                              size: 20,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              'Description',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: bookBranchGreen,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          fileMetadata
                                                  .fileDescription.isNotEmpty
                                              ? fileMetadata.fileDescription
                                              : 'No description available for this file.',
                                          style: TextStyle(
                                            fontSize: 15,
                                            color: Colors.grey.shade700,
                                            height: 1.5,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }
}

class FullScreenMediaView extends StatelessWidget {
  final String imageUrl;

  const FullScreenMediaView(this.imageUrl, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: PhotoView(
          imageProvider: NetworkImage(imageUrl),
          backgroundDecoration: BoxDecoration(
            color: Theme.of(context)
                .scaffoldBackgroundColor, // Match the background color to the scaffold
          ),
          minScale: PhotoViewComputedScale.contained * 0.8, // Minimum scale
          maxScale: PhotoViewComputedScale.covered * 2, // Maximum scale
          enableRotation: false, // Optionally enable rotation
        ),
      ),
    );
  }
}
