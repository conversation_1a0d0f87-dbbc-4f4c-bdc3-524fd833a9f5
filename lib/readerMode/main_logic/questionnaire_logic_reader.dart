import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class QuestionnaireLogicReader {
  final FirebaseAuth auth = FirebaseAuth.instance;

  // previous code archived

  Future<String> fetchProjectOwner(String projectID) async {
    // Reference to the Firestore 'projects' collection
    CollectionReference projects =
        FirebaseFirestore.instance.collection('projects');

    // Query the collection for documents matching the provided 'projectID'
    return projects
        .doc(projectID)
        .get()
        .then((DocumentSnapshot documentSnapshot) {
      return documentSnapshot['uid'];
    });
  }

  Future<bool> fetchSubscribedStatus(String uid, String projectID) async {
    // Reference to the Firestore 'applications' collection
    CollectionReference applications =
        FirebaseFirestore.instance.collection('applications');

    // Query the collection for documents matching both 'uid' and 'projectID'
    QuerySnapshot querySnapshot = await applications
        .where('uid', isEqualTo: uid)
        .where('projectID', isEqualTo: projectID)
        .get();

    // Check if there are any documents found
    if (querySnapshot.docs.isNotEmpty) {
      // Documents exist where both uid and projectID match the provided values
      return true;
    } else {
      // No documents found, the user is not subscribed to the project
      return false;
    }
  }

  Future<List<String>> fetchSentApplications(String uid) {
    // Reference to the Firestore 'applications' collection
    CollectionReference applications =
        FirebaseFirestore.instance.collection('applications');

    // Query the collection for documents matching the provided 'uid'
    return applications
        .where('uid', isEqualTo: uid)
        .get()
        .then((QuerySnapshot querySnapshot) {
      List<String> projectIDs = [];
      querySnapshot.docs.forEach((doc) {
        projectIDs.add(doc['projectName']);
      });
      return projectIDs;
    });
  }
}
