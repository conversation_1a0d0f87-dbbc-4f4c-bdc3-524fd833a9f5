import 'package:cloud_firestore/cloud_firestore.dart';

class DiscussionCommentsLogicReader {
  Future<Map<String, String>> fetchDiscussionFiles(String discussionID) async {
    Map<String, String> filesMap = {};

    // Access the 'discussionFiles' collection
    final collection = FirebaseFirestore.instance.collection('discussionFiles');

    // Query documents where 'discussionID' field matches the method parameter
    final querySnapshot =
        await collection.where('discussionID', isEqualTo: discussionID).get();

    // Iterate through the query snapshot and add 'fileUrl' and 'fileType' to the map
    querySnapshot.docs.forEach((doc) {
      final fileUrl = doc.data()['fileUrl'] as String;
      final fileType = doc.data()['fileType'] as String;
      filesMap[fileUrl] = fileType;
    });

    return filesMap;
  }

  Future<String> fetchDiscussionDescription(String discussionID) async {
    // Access the 'discussions' collection
    final collection = FirebaseFirestore.instance.collection('discussions');

    // Query documents where 'discussionID' field matches the method parameter
    final querySnapshot =
        await collection.where('discussionID', isEqualTo: discussionID).get();

    // Return the 'description' field of the first document in the query snapshot
    return querySnapshot.docs.first.data()['description'] as String;
  }
}
