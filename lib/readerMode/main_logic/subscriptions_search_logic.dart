import 'package:web_app/readerMode/main_logic/main_logic_fetch/fetch_reader.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/subscriptions_logic_fetch.dart';

class SubscriptionsSearchLogic {
  final FetchReader _fetchReader = FetchReader();
  final FirebaseAuth auth = FirebaseAuth.instance;

  final SubscriptionsLogicFetch _subscriptionsLogicFetch =
      SubscriptionsLogicFetch();

  Future<List<String>> searchSubscriptions(String query) async {
    final uid = auth.currentUser!.uid;

    // Fetch all project data
    List<Map<String, dynamic>> allProjectData =
        await _subscriptionsLogicFetch.fetchSubscribedProjectsDataForSearch(
            _subscriptionsLogicFetch.fetchSubscribedProjectIds(uid));

    // Initialize currentSearchResultsIDs map with priorities
    Map<String, List<String>> searchResultsByPriority = {
      'Project Name': [],
      'Display Name': [],
      'Others': []
    };

    // Convert query to lowercase for case-insensitive comparison
    String lowerCaseQuery = query.toLowerCase();

    // Iterate through all project data and search for the query
    for (var map in allProjectData) {
      bool found = false;

      // Check each entry in the map
      for (var entry in map.entries) {
        if (entry.value is String &&
            (entry.value as String).toLowerCase().contains(lowerCaseQuery)) {
          if (entry.key == 'Project Name') {
            searchResultsByPriority['Project Name']!.add(map['Project ID']);
            found = true;
            break;
          } else if (entry.key == 'Display Name') {
            searchResultsByPriority['Display Name']!.add(map['Project ID']);
            found = true;
            break;
          } else {
            searchResultsByPriority['Others']!.add(map['Project ID']);
            found = true;
            break;
          }
        } else if (entry.key == 'Tags' && entry.value is List) {
          List<dynamic> tags = entry.value as List<dynamic>;
          if (tags.any((tag) =>
              tag is String && tag.toLowerCase().contains(lowerCaseQuery))) {
            searchResultsByPriority['Others']!.add(map['Project ID']);
            found = true;
            break;
          }
        }
      }
    }

    // Combine results by priority
    List<String> currentSearchResultsIDs = [
      ...searchResultsByPriority['Project Name']!,
      ...searchResultsByPriority['Display Name']!,
      ...searchResultsByPriority['Others']!
    ];

    return currentSearchResultsIDs;
  }
}
