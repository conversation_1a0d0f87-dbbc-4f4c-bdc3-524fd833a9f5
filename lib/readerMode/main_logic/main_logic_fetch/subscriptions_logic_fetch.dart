import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class SubscriptionsLogicFetch {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<List<String>> fetchSubscribedProjectIds(String uid) async {
    final docRef = db.collection('projectSubscriptions').doc(uid);
    final docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return []; // Return an empty list if the document doesn't exist
    } else {
      // Assuming the structure is flat, with projectIds as keys directly in the document
      Map<String, dynamic>? data = docSnapshot.data();
      if (data != null) {
        // Filter out only those keys where the value is true (subscribed)
        List<String> subscribedIds = [];
        data.forEach((key, value) {
          if (value == true) {
            // Check if the value associated with the key is true
            subscribedIds.add(key);
          }
        });
        return subscribedIds;
      } else {
        return []; // If data is null or not found, return an empty list.
      }
    }
  }

  Future<int> fetchNumberOfSubscriptionsByUser(String uid) async {
    final docRef = db.collection('projectSubscriptions').doc(uid);
    final docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return 0; // Return 0 if the document doesn't exist
    } else {
      // Assuming the structure is flat, with projectIds as keys directly in the document
      Map<String, dynamic>? data = docSnapshot.data();
      if (data != null) {
        // Filter out only those keys where the value is true (subscribed)
        int count = 0;
        data.forEach((key, value) {
          if (value == true) {
            // Check if the value associated with the key is true
            count++;
            print("\n\n\n\n");
            print(count);
          }
        });
        return count;
      } else {
        return 0; // If data is null or not found, return 0.
      }
    }
  }

  Future<List<Map<String, String>>> fetchSubscribedProjectsData(
      List<String> projectIds) async {
    List<Map<String, String>> projectThumbnails = [];

    for (String projectId in projectIds) {
      DocumentSnapshot projectSnapshot =
          await db.collection('projects').doc(projectId).get();

      if (projectSnapshot.exists) {
        Map<String, dynamic> projectData =
            projectSnapshot.data() as Map<String, dynamic>;
        projectThumbnails.add({
          'Project ID': projectId,
          'Project Name': projectData['Project Name'],
          'thumbnail': projectData['thumbnail'] ?? 'default',
          'Description': projectData['Description'],
          'Display Name': projectData['Display Name'] ?? 'Anonymous'
        });
      }
    }
    return projectThumbnails;
  }

  Future<List<Map<String, dynamic>>> fetchSubscribedProjectsDataForSearch(
      Future<List<String>> projectIdsFuture) async {
    // Await the list of project IDs
    List<String> projectIds = await projectIdsFuture;

    // Use Future.wait to fetch all project data concurrently
    List<Map<String, dynamic>> projectThumbnails =
        await Future.wait(projectIds.map((projectId) async {
      DocumentSnapshot projectSnapshot =
          await db.collection('projects').doc(projectId).get();

      if (projectSnapshot.exists) {
        Map<String, dynamic> projectData =
            projectSnapshot.data() as Map<String, dynamic>;
        return {
          'Project ID': projectId,
          'Project Name': projectData['Project Name'],
          'thumbnail': projectData['thumbnail'] ?? 'default',
          'Description': projectData['Description'],
          'Display Name': projectData['Display Name'] ?? 'Anonymous',
          'Tags': projectData['tags'] ?? [],
        };
      }
      return {}; // If the project doesn't exist, return null
    }));

    // Filter out any null results
    return projectThumbnails
        .where((project) => project != null)
        .cast<Map<String, dynamic>>()
        .toList();
  }
}
