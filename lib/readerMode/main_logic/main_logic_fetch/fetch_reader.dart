import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FetchReader {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<List<Map<String, dynamic>>> fetchAllPublicProjects(
      {String? startAfter, int limit = 10}) async {
    Query query = db
        .collection('projects')
        .where('Visibility', isEqualTo: 'public')
        .limit(limit);

    if (startAfter != null) {
      DocumentSnapshot startAfterDoc =
          await db.collection('projects').doc(startAfter).get();
      query = query.startAfterDocument(startAfterDoc);
    }

    QuerySnapshot snapshot = await query.get();
    List<Map<String, dynamic>> publicProjects = [];

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
      publicProjects.add({
        'Project Name': data['Project Name'],
        'Project ID': doc.id,
        'Description': data['Description'],
        'uid': data['uid'],
        'thumbnail': data['thumbnail'] ?? 'default',
        'Display Name': data['Display Name'] ?? 'Anonymous'
      });
    }
    return publicProjects;
  }

  Future<List<Map<String, dynamic>>> fetchProjectDataByIDs(
      List<String> projectIDs) async {
    List<Map<String, dynamic>> projects = [];
    Set<String> uniqueProjectIDs = projectIDs.toSet(); // Ensure unique IDs

    for (String projectID in uniqueProjectIDs) {
      DocumentSnapshot snapshot =
          await db.collection('projects').doc(projectID).get();
      if (snapshot.exists) {
        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
        projects.add(data);
      }
    }
    return projects;
  }

  Future<bool> fetchSubscriptionStatus(String projectID) async {
    DocumentSnapshot snapshot = await db
        .collection('users')
        .doc(auth.currentUser!.uid)
        .collection('subscriptions')
        .doc(projectID)
        .get();

    if (snapshot.exists) {
      Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
      bool isSubscribed = data['Status'];
      return isSubscribed;
    }
    return false;
  }

  Future<Map<String, dynamic>> fetchAllUserData(String uid) async {
    DocumentSnapshot snapshot = await db.collection('users').doc(uid).get();
    Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
    return data;
  }

  Future<List<Map<String, dynamic>>> fetchAllPublicProjectThumbnails() async {
    List<Map<String, dynamic>> allPublicProjects =
        await fetchAllPublicProjects();
    // Transform the list of projects to include only project ID and thumbnail URL
    List<Map<String, dynamic>> thumbnails = allPublicProjects.map((project) {
      return {
        'Project ID': project['Project ID'],
        'thumbnail': project['thumbnail']
      };
    }).toList();

    return thumbnails;
  }

  Future<List<Map<String, dynamic>>>
      fetchPublicAndPrivateProjectsSearch() async {
    QuerySnapshot snapshot = await db.collection('projects').get();
    List<Map<String, dynamic>> publicProjects = [];

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
      //if (data['Visibility'] == 'public') {
      publicProjects.add({
        'Project Name': data['Project Name'],
        'Project ID': doc.id,
        'Description': data['Description'],
        'Display Name': data['Display Name'],
        'Tags': data['tags'] ?? [],
      });
      //}
    }
    return publicProjects;
  }
}
