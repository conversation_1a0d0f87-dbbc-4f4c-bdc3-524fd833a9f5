import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/upload_data.dart';
import 'package:web_app/readerMode/main_logic/discussions_state_management_reader.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/user_metadata_fetch.dart';
import 'package:tuple/tuple.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:web_app/readerMode/main_logic/questionnaire_logic_reader.dart';

class CommunityDiscussionsLogicReader {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;
  final UploadData dataObj = UploadData();

  QuestionnaireLogicReader questionnaireLogicReader =
      QuestionnaireLogicReader();

  //updateFileAndAggregateStorage ===***
  Future<void> createNewDiscussion(
    String discussionName,
    String description,
    List<String> attachments,
    List<PlatformFile> platformFiles,
    WidgetRef ref,
  ) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    if (uid == null) return;

    ValueNotifier<double> progressNotifier = ValueNotifier<double>(0.0);
    List<String?> fileUrls = []; // List to store URLs of uploaded files
    List<String> fileTypes = []; // List to store file types
    List<DocumentReference> fileDocRefs =
        []; // List to store file document references
    String username = user?.displayName ?? user?.email ?? "Unknown";

    try {
      // Create discussion document
      DocumentReference docRef = db.collection("discussions").doc();
      await docRef.set({
        'name': discussionName,
        'description': description,
        'projectID': ref.watch(currentProjectID),
        'discussionID': docRef.id,
        'likes': 0,
        'username': username,
        'uid': uid,
        'timestamp': FieldValue.serverTimestamp(),
        'Storage Used': 0,
      });

      // Pre-create file document references
      for (String fileName in attachments) {
        DocumentReference fileDocRef = db.collection("discussionFiles").doc();
        fileDocRefs.add(fileDocRef); // Store references for later updates
        await fileDocRef.set({
          'fileName': fileName,
          'uid': uid,
          'projectID': ref.watch(currentProjectID),
          'discussionID': docRef.id,
          'fileSize': 0, // Placeholder, updated by Cloud Function
        });
      }

      // Handle file uploads and metadata updates
      for (int i = 0; i < platformFiles.length; i++) {
        PlatformFile file = platformFiles[i];
        String fileType = determineFileType(file.name);
        fileTypes.add(fileType); // Store file type for reference

        // Upload file and update document with URL and fileType
        Map<String, dynamic> uploadResult =
            await dataObj.uploadFileToFirebaseStorageWithMetadata(
          file,
          ref,
          'discussionFiles/',
          (double progress) => progressNotifier.value = progress,
          SettableMetadata(customMetadata: {
            'userId': await questionnaireLogicReader.fetchProjectOwner(ref
                .watch(currentProjectID)), // needs to be uid of project owner
            'discussionId': docRef.id,
            'fileDocumentId': fileDocRefs[i]
                .id, // Ensure metadata includes the correct file document ID
            'projectId': ref.watch(currentProjectID),
          }),
        );

        String? fileUrl = uploadResult['fileUrl'] as String?;
        bool isDuplicate = uploadResult['isDuplicate'] as bool? ?? false;
        String? fileHash = uploadResult['fileHash'] as String?;

        fileUrls.add(fileUrl); // Store URL for reference

        // Update the same document with URL, fileType and fileHash
        await fileDocRefs[i].update({
          'fileUrl': fileUrl,
          'fileType': fileType,
          if (fileHash != null) 'fileHash': fileHash,
          'isDuplicate': isDuplicate,
        });
      }

      ref.invalidate(discussionsProvider);
      ref.refresh(discussionsProvider(ref.watch(currentProjectID)));
      print("Discussion and files posted successfully.");
    } catch (e) {
      print("Error posting discussion and files: $e");
    }
  }

  String determineFileType(String fileName) {
    final fileExtension = fileName.split('.').last.toLowerCase();
    switch (fileExtension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'Image';
      case 'pdf':
        return 'PDF';
      case 'mp4':
      case 'mov':
      case 'avi':
        return 'Video';
      default:
        return 'Other';
    }
  }

  Future<void> likeDiscussion(
      String discussionID, String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the user's liked and disliked discussions collections
      CollectionReference likedDiscussionsRef =
          db.collection("likedDiscussions");
      CollectionReference dislikedDiscussionsRef =
          db.collection("dislikedDiscussions");

      // Reference to the discussion document
      DocumentReference discussionDocRef =
          db.collection("discussions").doc(discussionID);

      // Check if the user has already liked the discussion
      DocumentSnapshot likedDocSnapshot =
          await likedDiscussionsRef.doc(discussionID).get();
      if (likedDocSnapshot.exists) {
        print("User has already liked this discussion.");
        return;
      }

      // If the user has disliked the discussion, neutralize the dislike first
      QuerySnapshot dislikedDocSnapshot = await dislikedDiscussionsRef
          .where('discussionID', isEqualTo: discussionID)
          .where('userID', isEqualTo: userID)
          .get();

      if (dislikedDocSnapshot.docs.isNotEmpty) {
        await dislikedDiscussionsRef.doc(discussionID).delete();
        // Neutralizing the dislike by incrementing likes
        await discussionDocRef.update({
          'likes': FieldValue.increment(1),
        });

        // Refresh the provider to update the UI
        ref.refresh(discussionLikesProvider(discussionID));

        //ref.refresh(discussionsProvider((discussionID)));
        print("Discussion neutralized from dislike.");
        return;
      }

      // Now, like the discussion
      await likedDiscussionsRef.doc(discussionID).set({
        'discussionID': discussionID,
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await discussionDocRef.update({
        'likes': FieldValue.increment(1),
      });

      // Refresh the provider to update the UI
      ref.refresh(discussionLikesProvider(discussionID));

      //ref.refresh(discussionsProvider((discussionID)));
      print("Discussion liked successfully.");
    } catch (e) {
      print("Error liking discussion: $e");
    }
  }

  Future<void> dislikeDiscussion(
      String discussionID, String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the user's liked and disliked discussions collections
      CollectionReference likedDiscussionsRef =
          db.collection("likedDiscussions");
      CollectionReference dislikedDiscussionsRef =
          db.collection("dislikedDiscussions");

      // Reference to the discussion document
      DocumentReference discussionDocRef =
          db.collection("discussions").doc(discussionID);

      // Check if the user has already disliked the discussion
      DocumentSnapshot dislikedDocSnapshot =
          await dislikedDiscussionsRef.doc(discussionID).get();
      if (dislikedDocSnapshot.exists) {
        print("User has already disliked this discussion.");
        return;
      }

      // If the user has liked the discussion, neutralize the like first
      QuerySnapshot likedDocSnapshot = await likedDiscussionsRef
          .where('discussionID', isEqualTo: discussionID)
          .where('userID', isEqualTo: userID)
          .get();

      if (likedDocSnapshot.docs.isNotEmpty) {
        await likedDiscussionsRef.doc(likedDocSnapshot.docs.first.id).delete();
        // Neutralizing the like by decrementing likes
        await discussionDocRef.update({
          'likes': FieldValue.increment(-1),
        });

        // Refresh the provider to update the UI
        ref.refresh(discussionLikesProvider(discussionID));

        print("Like neutralized for the discussion.");
        return;
      }

      // Now, dislike the discussion
      await dislikedDiscussionsRef.doc(discussionID).set({
        'discussionID': discussionID,
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await discussionDocRef.update({
        'likes': FieldValue.increment(-1),
      });

      // Refresh the provider to update the UI
      ref.refresh(discussionLikesProvider(discussionID));

      print("Discussion disliked successfully.");
    } catch (e) {
      print("Error disliking discussion: $e");
    }
  }

  Future<void> likeComment(String discussionID, String commentID, String userID,
      WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef =
          db.collection("likedDiscussionComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedDiscussionComments");

      // Reference to the comment document within a specific discussion
      DocumentReference commentDocRef = db
          .collection("discussions")
          .doc(discussionID)
          .collection('comments')
          .doc(commentID);

      // Check if the user has already liked the comment
      DocumentSnapshot likedDocSnapshot =
          await likedCommentsRef.doc(commentID).get();
      if (likedDocSnapshot.exists) {
        print("User has already liked this comment.");
        return;
      }

      // If the user has disliked the comment, neutralize the dislike first
      QuerySnapshot dislikedDocSnapshot = await dislikedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (dislikedDocSnapshot.docs.isNotEmpty) {
        await dislikedCommentsRef
            .doc(dislikedDocSnapshot.docs.first.id)
            .delete();
        // Neutralizing the dislike by incrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(1),
        });

        // Refresh the provider to update the UI
        ref.refresh(
            discussionCommentsLikesProvider(Tuple2(discussionID, commentID)));

        print("Comment neutralized from dislike.");
        return;
      }

      // Now, like the comment
      await likedCommentsRef.doc(commentID).set({
        'commentID': commentID,
        'discussionID': discussionID,
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(1),
      });

      ref.refresh(
          discussionCommentsLikesProvider(Tuple2(discussionID, commentID)));

      // Refresh the provider to update the UI
      print("Comment liked successfully.");
    } catch (e) {
      print("Error liking comment: $e");
    }
  }

  Future<void> dislikeComment(String discussionID, String commentID,
      String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef =
          db.collection("likedDiscussionComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedDiscussionComments");

      // Reference to the comment document within a specific discussion
      DocumentReference commentDocRef = db
          .collection("discussions")
          .doc(discussionID)
          .collection('comments')
          .doc(commentID);

      // Check if the user has already disliked the comment
      DocumentSnapshot dislikedDocSnapshot =
          await dislikedCommentsRef.doc(commentID).get();
      if (dislikedDocSnapshot.exists) {
        print("User has already disliked this comment.");
        return;
      }

      // If the user has liked the comment, neutralize the like first
      QuerySnapshot likedDocSnapshot = await likedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (likedDocSnapshot.docs.isNotEmpty) {
        await likedCommentsRef.doc(likedDocSnapshot.docs.first.id).delete();
        // Neutralizing the like by decrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(-1),
        });

        // Refresh the provider to update the UI
        ref.refresh(
            discussionCommentsLikesProvider(Tuple2(discussionID, commentID)));

        print("Like neutralized for the comment.");
        return;
      }

      // Now, dislike the comment
      await dislikedCommentsRef.doc(commentID).set({
        'commentID': commentID,
        'discussionID': discussionID,
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(-1),
      });

      ref.refresh(
          discussionCommentsLikesProvider(Tuple2(discussionID, commentID)));

      // Refresh the provider to update the UI
      print("Comment disliked successfully.");
    } catch (e) {
      print("Error disliking comment: $e");
    }
  }

  Future<void> likeNestedComment(String discussionID, String parentCommentID,
      String commentID, String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef =
          db.collection("likedNestedDiscussionComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedNestedDiscussionComments");

      // Reference to the nested comment document within a specific discussion and parent comment
      DocumentReference commentDocRef = db
          .collection("discussions")
          .doc(discussionID)
          .collection("comments")
          .doc(parentCommentID)
          .collection('nestedComments')
          .doc(commentID);

      // Check if the user has already liked the comment
      DocumentSnapshot likedDocSnapshot =
          await likedCommentsRef.doc(commentID).get();
      if (likedDocSnapshot.exists) {
        print("User has already liked this comment.");
        return;
      }

      // If the user has disliked the comment, neutralize the dislike first
      QuerySnapshot dislikedDocSnapshot = await dislikedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (dislikedDocSnapshot.docs.isNotEmpty) {
        await dislikedCommentsRef
            .doc(dislikedDocSnapshot.docs.first.id)
            .delete();
        // Neutralizing the dislike by incrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(1),
        });

        ref.invalidate(discussionCommentsNestedLikesProvider(
            Tuple3(discussionID, parentCommentID, commentID)));
        ref.refresh(discussionCommentsNestedLikesProvider(
            Tuple3(discussionID, parentCommentID, commentID)));

        print("Comment neutralized from dislike.");
        return;
      }

      // Now, like the comment
      await likedCommentsRef.doc(commentID).set({
        'commentID': commentID,
        'parentCommentID': parentCommentID,
        'discussionID': discussionID,
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(1),
      });

      ref.invalidate(discussionCommentsNestedLikesProvider(
          Tuple3(discussionID, parentCommentID, commentID)));
      ref.refresh(discussionCommentsNestedLikesProvider(
          Tuple3(discussionID, parentCommentID, commentID)));

      print("Comment liked successfully.");
    } catch (e) {
      print("Error liking comment: $e");
    }
  }

  Future<void> dislikeNestedComment(String discussionID, String parentCommentID,
      String commentID, String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef =
          db.collection("likedNestedDiscussionComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedNestedDiscussionComments");

      // Reference to the nested comment document within a specific discussion and parent comment
      DocumentReference commentDocRef = db
          .collection("discussions")
          .doc(discussionID)
          .collection("comments")
          .doc(parentCommentID)
          .collection('nestedComments')
          .doc(commentID);

      // Check if the user has already disliked the comment
      DocumentSnapshot dislikedDocSnapshot =
          await dislikedCommentsRef.doc(commentID).get();
      if (dislikedDocSnapshot.exists) {
        print("User has already disliked this comment.");
        return;
      }

      // If the user has liked the comment, neutralize the like first
      QuerySnapshot likedDocSnapshot = await likedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (likedDocSnapshot.docs.isNotEmpty) {
        await likedCommentsRef.doc(likedDocSnapshot.docs.first.id).delete();
        // Neutralizing the like by decrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(-1),
        });

        ref.invalidate(discussionCommentsNestedLikesProvider(
            Tuple3(discussionID, parentCommentID, commentID)));
        ref.refresh(discussionCommentsNestedLikesProvider(
            Tuple3(discussionID, parentCommentID, commentID)));

        print("Like neutralized for the comment.");
        return;
      }

      // Now, dislike the comment
      await dislikedCommentsRef.doc(commentID).set({
        'commentID': commentID,
        'parentCommentID': parentCommentID,
        'discussionID': discussionID,
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(-1),
      });

      ref.invalidate(discussionCommentsNestedLikesProvider(
          Tuple3(discussionID, parentCommentID, commentID)));
      ref.refresh(discussionCommentsNestedLikesProvider(
          Tuple3(discussionID, parentCommentID, commentID)));

      print("Comment disliked successfully.");
    } catch (e) {
      print("Error disliking comment: $e");
    }
  }

  Future<void> postDiscussionComment(
      String discussionID, String comment, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    UserMetadataFetch? userData =
        await UserMetadataFetch.fetchUserMetaData(uid!);
    final displayName = userData!.displayName;

    try {
      // Generate a new document reference for the comment
      DocumentReference docRef = db
          .collection("discussions")
          .doc(discussionID)
          .collection('comments')
          .doc();

      // Create the comment data
      Map<String, dynamic> commentData = {
        'comment': comment,
        'discussionID': ref.watch(currentDiscussionIDProvider),
        'userID': auth.currentUser!.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'likes': 0,
        'displayName': displayName,
        'commentID': docRef.id,
        'projectID': ref.watch(currentProjectID),
        'discussionTitle': ref.watch(currentDiscussionTitleProvider),
      };

      // Write the comment data to the discussion's comments collection
      await docRef.set(commentData);

      // Check if the user has notifications enabled
      bool notificationsEnabled =
          await ref.read(notificationsEnabledProvider.notifier).state;
      if (notificationsEnabled) {
        // Write the comment data to the notifications collection
        await db.collection('notifications').doc(docRef.id).set(commentData);
      }

      // Invalidate and refresh the comments provider
      ref.invalidate(discussionCommentsProvider);
      ref.refresh(discussionCommentsProvider(discussionID));

      print("Comment posted successfully.");
    } catch (e) {
      print("Error posting comment: $e");
    }
  }

  Future<void> postNestedDiscussionComment(String comment, String discussionID,
      String commentID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    UserMetadataFetch? userData =
        await UserMetadataFetch.fetchUserMetaData(uid!);
    final displayName = userData!.displayName;

    try {
      String fileUrl = ref.watch(chosenFileUrl);
      DocumentReference docRef = db
          .collection("discussions")
          .doc(discussionID)
          .collection("comments")
          .doc(commentID)
          .collection('nestedComments')
          .doc();

      await docRef.set({
        'comment': comment,
        'discussionID': discussionID,
        'userID': auth.currentUser!.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'likes': 0,
        'displayName': displayName,
        'commentID': docRef.id,
        'parentCommentID': commentID,
        'projectID': ref.watch(currentProjectID),
      });

      ref.refresh(discussionCommentsRepliesProvider(
          Tuple2(ref.watch(currentDiscussionIDProvider), commentID)));

      print("Comment posted successfully.");
    } catch (e) {
      print("Error posting comment: $e");
    }
  }
}
