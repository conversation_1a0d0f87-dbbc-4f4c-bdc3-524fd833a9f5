import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';

class CommunityDiscussionsFetchReader {
  Future<List<List<dynamic>>?> fetchDiscussions(
      WidgetRef ref, String filter) async {
    FirebaseFirestore db = FirebaseFirestore.instance;

    List<String> discussionTitles = [];
    List<String> discussionDescriptions = [];
    List<Timestamp> discussionTimestamps = [];
    List<String> discussionIDs = [];
    List<String> projectIDs = [];
    List<int> likesList = [];
    List<String?> usernames = [];
    List<String> userIDs = [];
    List<List<dynamic>> everything = [];

    String sortOrderField = 'timestamp'; // Default to sorting by "Newest"
    bool descending = true; // Newest discussions first

    if (filter == 'Trending') {
      sortOrderField = 'likes'; // Change to sorting by likes for "Trending"
    }

    final QuerySnapshot = await db
        .collection('discussions')
        .where('projectID', isEqualTo: ref.watch(currentProjectID))
        .orderBy(sortOrderField, descending: descending)
        .get();

    if (QuerySnapshot.docs.isNotEmpty) {
      for (var doc in QuerySnapshot.docs) {
        discussionTitles.add(doc['name'] ?? "Untitled");
        discussionDescriptions
            .add(doc['description'] ?? "No description available.");
        discussionTimestamps.add(doc['timestamp'] as Timestamp);
        discussionIDs.add(doc['discussionID']);
        projectIDs.add(doc['projectID']);
        likesList.add(doc['likes'] ?? 0);
        userIDs.add(doc['uid']);
        usernames.add(doc['username']);
      }

      everything.add(discussionTitles);
      everything.add(discussionDescriptions);
      everything.add(discussionTimestamps);
      everything.add(discussionIDs);
      everything.add(projectIDs);
      everything.add(likesList);
      everything.add(usernames);
      everything.add(userIDs);

      return everything;
    } else {
      // If no discussions are found, return null
      return null;
    }
  }

  Future<List<List<dynamic>>> fetchDiscussionComments(
      WidgetRef ref, String discussionID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;

    List<String> commentsList = [];
    List<String> displayNameList = [];
    List<int> likesList = [];
    List<Timestamp> timeStampList = [];
    List<List<dynamic>> everything = [];
    List<String> commentIDList = [];

    try {
      final querySnapshot = await db
          .collection('discussions')
          .doc(discussionID)
          .collection('comments')
          .orderBy('timestamp', descending: true)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        for (var doc in querySnapshot.docs) {
          var commentData = doc['comment'];
          var displayName = doc['displayName'];
          var likes = doc['likes'];
          var timeStamp = doc['timestamp'];
          var commentID = doc['commentID'];
          if (commentData is String) {
            commentsList.add(commentData);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (displayName is String) {
            displayNameList.add(displayName);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (likes is int) {
            likesList.add(likes);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (timeStamp is Timestamp) {
            timeStampList.add(timeStamp);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (commentID is String) {
            commentIDList.add(commentID);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
        }
        everything.add(displayNameList);
        everything.add(timeStampList);
        everything.add(commentsList);
        everything.add(likesList);
        everything.add(commentIDList);
        print("Comments fetched successfully.");
        print(everything);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error fetching comments: $e");
    }
    return everything;
  }

  Future<List<dynamic>> fetchNestedComments(
      String discussionID, String parentCommentID, WidgetRef ref,
      {int currentDepth = 0}) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    List<dynamic> comments = [];
    //currentDepth = 0;

    try {
      var querySnapshot = await db
          .collection("discussions")
          .doc(discussionID)
          .collection("comments")
          .doc(parentCommentID)
          .collection('nestedComments')
          .orderBy('likes', descending: true)
          .get();

      for (var doc in querySnapshot.docs) {
        var comment = {
          'displayName': doc['displayName'],
          'likes': doc['likes'],
          'timestamp': (doc['timestamp'] as Timestamp).toDate(),
          'comment': doc['comment'],
          'commentID': doc.id,
          'parentCommentID': parentCommentID,
          'depth': currentDepth, // Add current depth to the comment info
          'nestedComments': [],
        };

        // Recursively fetch nested comments and increment depth
        var nestedComments = await fetchNestedComments(
            doc.id, discussionID, ref,
            currentDepth: currentDepth + 1);
        if (nestedComments.isNotEmpty) {
          comment['nestedComments'] = nestedComments;
        }

        comments.add(comment);
      }
    } catch (e) {
      print("Error fetching nested comments: $e");
    }

    return comments;
  }

  Future<int> fetchNumComments(String discussionID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    final QuerySnapshot = db
        .collection('discussions')
        .doc(discussionID)
        .collection('comments')
        .get();
    return QuerySnapshot.then((value) => value.docs.length);
  }

  Future<int> fetchNumDiscussions(String projectID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    final QuerySnapshot = await db
        .collection('discussions')
        .where('projectID', isEqualTo: projectID)
        .get();
    return QuerySnapshot.docs.length;
  }

  Future<int> fetchNumDiscussionLikes(String discussionID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    try {
      var docSnapshot =
          await db.collection('discussions').doc(discussionID).get();
      int numberOfLikes = docSnapshot['likes'];
      return numberOfLikes;
    } catch (e) {
      print("Error fetching number of likes: $e");
      return 0;
    }
  }

  Future<int> fetchNumberOfLikes(String discussionID, String commentID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    try {
      var docSnapshot = await db
          .collection("discussions")
          .doc(discussionID)
          .collection('comments')
          .doc(commentID)
          .get();
      int numberOfLikes = docSnapshot['likes'];
      return numberOfLikes;
    } catch (e) {
      print("Error fetching number of likes: $e");
      return 0;
    }
  }

  Future<int> fetchNumberOfNestedLikes(
      String discussionID, String parentCommentID, String commentID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    try {
      var docSnapshot = await db
          .collection("discussions")
          .doc(discussionID)
          .collection('comments')
          .doc(parentCommentID)
          .collection('nestedComments')
          .doc(commentID)
          .get();
      int numberOfLikes = docSnapshot['likes'];
      return numberOfLikes;
    } catch (e) {
      print("Error fetching number of likes: $e");
      return 0;
    }
  }

  Future<int> fetchNumberOfDiscussionReplies(
      String discussionID, String parentCommentID) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    try {
      var querySnapshot = await db
          .collection("discussions")
          .doc(discussionID)
          .collection('comments')
          .doc(parentCommentID)
          .collection('nestedComments')
          .where('parentCommentID', isEqualTo: parentCommentID)
          .get();

      int numberOfReplies = querySnapshot.docs.length;
      return numberOfReplies;
    } catch (e) {
      print("Error fetching number of replies: $e");
      return 0;
    }
  }
}
