import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class SubscriptionsLogic {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  // return the project name and thumbnail of subscribed projects
  Future<List<Map<String, String>>> getSubscribedProjectsData(
      List<String> projectIds) async {
    List<Map<String, String>> projectThumbnails = [];

    for (String projectId in projectIds) {
      DocumentSnapshot projectSnapshot =
          await db.collection('projects').doc(projectId).get();

      if (projectSnapshot.exists) {
        Map<String, dynamic> projectData =
            projectSnapshot.data() as Map<String, dynamic>;
        projectThumbnails.add({
          'Project ID': projectId,
          'Project Name': projectData['Project Name'],
          'thumbnail': projectData['thumbnail'] ?? 'default',
          'Description': projectData['Description'],
        });
      }
    }
    return projectThumbnails;
  }
}
