import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/fetch_reader.dart';

class MarketPlaceLogic {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;
  FetchReader fetch = FetchReader();

  Future<void> subscribeToProject(String projectId, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    if (uid == null) return; // Stop if no user is logged in

    await db.runTransaction((transaction) async {
      // Get the project document reference and snapshot within the same transaction
      final projectDocRef = db.collection('projects').doc(projectId);
      final projectSnapshot = await transaction.get(projectDocRef);

      final docRef = db.collection('projectSubscriptions').doc(uid);
      final docSnapshot = await transaction.get(docRef);

      if (!docSnapshot.exists) {
        // Document does not exist, create it initially with a subscription to the current project
        transaction.set(docRef, {ref.watch(currentProjectID): true});
      } else {
        // Document exists, append or update the subscriptions map
        transaction.update(docRef, {ref.watch(currentProjectID): true});
      }

      if (projectSnapshot.exists) {
        var currentSubscribers = projectSnapshot.data()!['Subscribers'] ?? 0;
        currentSubscribers += 1; // Increment the subscriber count
        transaction.update(projectDocRef, {'Subscribers': currentSubscribers});
      }
    });
  }

  Future<void> unsubscribeFromProject(String projectId, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    if (uid == null) return; // Stop if no user is logged in

    await db.runTransaction((transaction) async {
      // Get the project document reference and snapshot within the same transaction
      final projectDocRef = db.collection('projects').doc(projectId);
      final projectSnapshot = await transaction.get(projectDocRef);

      final docRef = db.collection('projectSubscriptions').doc(uid);
      final docSnapshot = await transaction.get(docRef);

      // Proceed only if the document exists and the user is currently subscribed to the project
      if (docSnapshot.exists && docSnapshot.data()![projectId] == true) {
        // Use FieldValue.delete() to remove the field corresponding to projectId
        transaction.update(docRef, {projectId: FieldValue.delete()});

        if (projectSnapshot.exists &&
            (projectSnapshot.data()!['Subscribers'] ?? 0) > 0) {
          var currentSubscribers =
              projectSnapshot.data()!['Subscribers'] as int;
          currentSubscribers -= 1; // Decrement the subscriber count
          transaction
              .update(projectDocRef, {'Subscribers': currentSubscribers});
        }
      }
    }).catchError((error) {
      print("Failed to unsubscribe: $error");
    });

    ref.refresh(numberOfSubscribedProjectsByUserProvider);
  }

  Future<bool> isSubscribedToProject(String currentProjectID) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    if (uid == null) {
      return false; // User not logged in
    }

    final docRef = db.collection('projectSubscriptions').doc(uid);
    final docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return false; // Subscription document doesn't exist for the user
    }

    // Directly access the project ID field at the root of the document
    bool isSubscribed = docSnapshot.data()?[currentProjectID] ==
        true; // Checks if the value is true
    return isSubscribed;
  }
}
