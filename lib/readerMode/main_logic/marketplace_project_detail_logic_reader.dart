import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class MarketPlaceProjectDetailLogicReader {
  FirebaseFirestore db = FirebaseFirestore.instance;

  Future<bool> isPrivateProject(String projectID, WidgetRef ref) async {
    try {
      DocumentSnapshot projectDoc =
          await db.collection('projects').doc(projectID).get();

      if (projectDoc.exists) {
        var visibility = projectDoc.get('Visibility');
        return visibility == 'private';
      } else {
        return false; // Document doesn't exist, assume public
      }
    } catch (e) {
      print('Error fetching project visibility: $e');
      return false; // In case of error, assume public
    }
  }
}
