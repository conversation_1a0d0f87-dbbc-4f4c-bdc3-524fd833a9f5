import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tuple/tuple.dart';
import 'package:web_app/readerMode/main_logic/discussions_fetch_reader.dart';

final discussionsProvider =
    FutureProvider.family<int, String>((ref, projectID) async {
  return CommunityDiscussionsFetchReader().fetchNumDiscussions(projectID);
});

final discussionLikesProvider =
    FutureProvider.family<int, String>((ref, discussionID) async {
  return CommunityDiscussionsFetchReader()
      .fetchNumDiscussionLikes(discussionID);
});

final currentDiscussionIDProvider = StateProvider<String>((ref) {
  return "";
});

final currentDiscussionTitleProvider = StateProvider<String>((ref) {
  return "";
});

final currentDiscussionDescriptionProvider = StateProvider<String>((ref) {
  return "";
});

final discussionCommentsProvider =
    FutureProvider.family<int, String>((ref, discussionID) async {
  return CommunityDiscussionsFetchReader().fetchNumComments(discussionID);
});

final discussionCommentsLikesProvider =
    FutureProvider.family<int, Tuple2<String, String>>((ref, tuple) async {
  final discussionID = tuple.item1;
  final commentID = tuple.item2;
  return CommunityDiscussionsFetchReader()
      .fetchNumberOfLikes(discussionID, commentID);
});

final discussionCommentsNestedLikesProvider =
    FutureProvider.family<int, Tuple3<String, String, String>>(
        (ref, tuple) async {
  return CommunityDiscussionsFetchReader()
      .fetchNumberOfNestedLikes(tuple.item1, tuple.item2, tuple.item3);
});

final discussionCommentsRepliesProvider =
    FutureProvider.family<int, Tuple2<String, String>>((ref, tuple) async {
  return CommunityDiscussionsFetchReader()
      .fetchNumberOfDiscussionReplies(tuple.item1, tuple.item2);
});
