import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MainScreenLogicReader {
  Future<List<Map<String, dynamic>>> fetchAllUserDiscussionComments(
      String uid, WidgetRef ref) async {
    FirebaseFirestore db = FirebaseFirestore.instance;

    List<Map<String, dynamic>> commentsData = [];

    try {
      // Query the notifications collection where userID matches the provided uid
      final commentsSnapshot = await db
          .collection('notifications')
          .where('userID', isEqualTo: uid)
          .orderBy('timestamp', descending: true)
          .get();

      if (commentsSnapshot.docs.isNotEmpty) {
        for (var commentDoc in commentsSnapshot.docs) {
          commentsData.add({
            'commentID': commentDoc['commentID'],
            'displayName': commentDoc['displayName'],
            'timestamp': commentDoc['timestamp'],
            'comment': commentDoc['comment'],
            'likes': commentDoc['likes'],
            'discussionTitle': commentDoc['discussionTitle'],
            'userID': commentDoc['userID']
          });
        }
      } else {}
    } catch (e) {
      print("Error fetching comments: $e");
    }
    return commentsData;
  }
}
