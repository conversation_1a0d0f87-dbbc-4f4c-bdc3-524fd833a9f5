import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/account_delete_logic.dart';

class AccountDetailsLogic {
  AccountDeleteLogic deleteLogic = AccountDeleteLogic();
  Future<void> upgradePlanType(WidgetRef ref) async {
    final FirebaseAuth auth = FirebaseAuth.instance;
    final FirebaseFirestore db = FirebaseFirestore.instance;
    final User? user = auth.currentUser;
    if (user != null) {
      final String uid = user.uid;
      final DocumentReference userDoc = db.collection('users').doc(uid);
      const String newPlanType = 'BookBranch+';
      await userDoc.update({'Account Type': newPlanType});
    }
    ref.read(planTypeProvider.notifier).state = 'BookBranch+';
  }

  Future<void> downgradePlanType(WidgetRef ref) async {
    final FirebaseAuth auth = FirebaseAuth.instance;
    final FirebaseFirestore db = FirebaseFirestore.instance;
    final User? user = auth.currentUser;

    if (user != null) {
      final String uid = user.uid;
      final DocumentReference userDoc = db.collection('users').doc(uid);
      const String newPlanType = 'BookBranch';

      try {
        // Attempt to update the user document with the new account type
        await userDoc.update({'Account Type': newPlanType});

        // Proceed to delete all projects for the user
        await deleteLogic.deleteAllProjectsForUser(uid, ref);

        // Update the plan type state
        ref.read(planTypeProvider.notifier).state = 'BookBranch';
      } catch (e) {
        // Handle any errors that occur during the update or deletion process
        print('Error when downgrading plan type: $e');
        // Optionally, handle the error state in your application
      }
    } else {
      print('No user logged in to downgrade.');
    }
  }
}
