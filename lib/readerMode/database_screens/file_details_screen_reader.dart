import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/database_widgets/file_details_widget_reader.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FilesDetailsScreenReader extends ConsumerWidget {
  final String fileUrl; // Add this line
  final String? documentId; // Optional document ID
  final String?
      expectedDisplayName; // Expected display name to help identify the correct duplicate
  final String?
      expectedDescription; // Expected description to help identify the correct duplicate
  final Timestamp?
      expectedTimestamp; // Expected timestamp to help identify the correct duplicate

  // Modify the constructor to accept fileUrl and optional identifiers
  FilesDetailsScreenReader({
    required this.fileUrl,
    this.documentId,
    this.expectedDisplayName,
    this.expectedDescription,
    this.expectedTimestamp,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //return FileDetailsWidgetReader();
    return FileDetailsWidgetReader(
      fileUrl: fileUrl,
      documentId: documentId,
      expectedDisplayName: expectedDisplayName,
      expectedDescription: expectedDescription,
      expectedTimestamp: expectedTimestamp,
    );
  }
}
