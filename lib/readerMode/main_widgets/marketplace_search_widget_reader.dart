import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_logic/marketplace_search_logic_reader.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/fetch_reader.dart';
import 'package:web_app/readerMode/main_screens/marketplace_project_detail_screen_reader.dart';

class MarketPlaceSearchWidgetReader extends ConsumerStatefulWidget {
  @override
  _MarketPlaceSearchWidgetReaderState createState() =>
      _MarketPlaceSearchWidgetReaderState();
}

class _MarketPlaceSearchWidgetReaderState
    extends ConsumerState<MarketPlaceSearchWidgetReader> {
  final TextEditingController _searchController = TextEditingController();
  bool _isTyping = false;
  final MarketPlaceSearchLogicReader _marketPlaceSearchLogicReader =
      MarketPlaceSearchLogicReader();
  final FetchReader _fetchReader = FetchReader();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _isTyping = _searchController.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marketplace Search'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText:
                    _isTyping ? '' : 'Search for authors, titles, tags...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                prefixIcon: const Icon(Icons.search),
              ),
              autofocus: true,
            ),
          ),
          Expanded(
            child: FutureBuilder<List<Map<String, dynamic>>>(
              future: _marketPlaceSearchLogicReader
                  .searchMarketPlace(_searchController.text)
                  .then(
                (projectIDs) async {
                  if (projectIDs.isEmpty) {
                    return [];
                  }
                  return await _fetchReader.fetchProjectDataByIDs(projectIDs);
                },
              ),
              builder: (context,
                  AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: SizedBox(
                      width:
                          50, // Fixed width to ensure the indicator is circular
                      height: 50, // Fixed height to match the width
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                            Color.fromARGB(255, 44, 148, 44)),
                      ),
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Text('Error: ${snapshot.error}');
                } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Text('No data available.');
                } else {
                  List<Map<String, dynamic>> projects = snapshot.data!;
                  // Assuming you have a way to fetch thumbnails and user data similar to the displayMarketPlaceMobile method
                  return GridView.builder(
                    padding: const EdgeInsets.all(0),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 1, // Only one item per row
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio:
                          3 / 1.3, // Adjust the aspect ratio as needed
                    ),
                    itemCount: projects.length,
                    itemBuilder: (context, i) {
                      var project = projects[i];
                      // Assuming you have a way to get the thumbnailUrl and users data similar to the displayMarketPlaceMobile method
                      String thumbnailUrl = 'default'; // Placeholder
                      bool isDefaultThumbnail = thumbnailUrl == 'default';

                      return isDefaultThumbnail
                          ? defaultProjectButton(ref, context, project, {})
                          : imageProjectButton(
                              ref, context, project, {}, thumbnailUrl);
                    },
                    physics: const BouncingScrollPhysics(),
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget defaultProjectButton(WidgetRef ref, BuildContext context,
      Map<String, dynamic> project, Map<String, dynamic> users) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      width: double.infinity,
      child: Stack(
        children: [
          // Decorative element - top gradient bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 6,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    bookBranchGreen,
                    Color.fromARGB(178, 44, 148, 44),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),
          // Decorative element - corner accent
          Positioned(
            top: 6,
            right: 0,
            child: Container(
              height: 24,
              width: 24,
              decoration: const BoxDecoration(
                color: lightGreen,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                ),
              ),
            ),
          ),
          // Main content
          InkWell(
            onTap: () {
              ref.read(currentProjectID.notifier).state = project['Project ID'];
              ref.read(currentProjectName.notifier).state =
                  project['Project Name'];
              ref.read(projectDescriptionReaderProvider.notifier).state =
                  project['Description'];
              ref.read(currentProjectThumbnailUrlProvider.notifier).state =
                  project['thumbnail'];
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return MarketPlaceProjectDetailScreenReader();
              }));
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Row for icon and project name
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project icon placeholder
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: lightGreen,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.book_outlined,
                          color: bookBranchGreen,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Project name and author in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Project name
                            Text(
                              project['Project Name'],
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // Author info with icon
                            Row(
                              children: [
                                const Icon(
                                  Icons.person_outline,
                                  size: 14,
                                  color: bookBranchGreen,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Author: ${project['Display Name']}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget imageProjectButton(
      WidgetRef ref,
      BuildContext context,
      Map<String, dynamic> project,
      Map<String, dynamic> users,
      String thumbnailUrl) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      width: double.infinity,
      child: Stack(
        children: [
          // Decorative element - top gradient bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 6,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    bookBranchGreen,
                    Color.fromARGB(178, 44, 148, 44),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),
          // Decorative element - corner accent
          Positioned(
            top: 6,
            right: 0,
            child: Container(
              height: 24,
              width: 24,
              decoration: const BoxDecoration(
                color: lightGreen,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                ),
              ),
            ),
          ),
          // Main content
          InkWell(
            onTap: () {
              ref.read(currentProjectID.notifier).state = project['Project ID'];
              ref.read(currentProjectName.notifier).state =
                  project['Project Name'];
              ref.read(projectDescriptionReaderProvider.notifier).state =
                  project['Description'];
              ref.read(currentProjectThumbnailUrlProvider.notifier).state =
                  project['thumbnail'];
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return MarketPlaceProjectDetailScreenReader();
              }));
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Row for thumbnail and project name
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project thumbnail
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: thumbnailUrl != 'default'
                            ? Image.network(
                                thumbnailUrl,
                                width: 40,
                                height: 40,
                                fit: BoxFit.cover,
                              )
                            : Container(
                                width: 40,
                                height: 40,
                                color: lightGreen,
                                child: const Icon(
                                  Icons.book_outlined,
                                  color: bookBranchGreen,
                                  size: 24,
                                ),
                              ),
                      ),
                      const SizedBox(width: 12),
                      // Project name and author in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Project name
                            Text(
                              project['Project Name'],
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // Author info with icon
                            Row(
                              children: [
                                const Icon(
                                  Icons.person_outline,
                                  size: 14,
                                  color: bookBranchGreen,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Author: ${project['Display Name']}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
