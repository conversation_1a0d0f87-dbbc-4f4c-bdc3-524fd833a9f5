import 'package:flutter/material.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_logic/discussions_state_management_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/readerMode/main_logic/discussions_logic_reader.dart';
import 'package:web_app/readerMode/main_logic/discussions_fetch_reader.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:web_app/readerMode/main_widgets/discussions_comments_widget_reader.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/database/database_logic/discussions_file_selector.dart';
import 'package:file_picker/file_picker.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/services.dart'; // for SystemChannels
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class CommunityDiscussionsWidgetReader extends ConsumerWidget {
  const CommunityDiscussionsWidgetReader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(discussionsProvider(ref.watch(currentProjectID)));

    return const MaterialApp(
      title: 'Project Discussions',
      home: ProjectDiscussionPage(),
    );
  }
}

class ProjectDiscussionPage extends StatefulWidget {
  const ProjectDiscussionPage({super.key});

  @override
  State<ProjectDiscussionPage> createState() => _ProjectDiscussionPageState();
}

class _ProjectDiscussionPageState extends State<ProjectDiscussionPage> {
  UniversalWidgets universals = UniversalWidgets();
  final CommunityDiscussionsLogicReader logic =
      CommunityDiscussionsLogicReader();
  final CommunityDiscussionsFetchReader fetcher =
      CommunityDiscussionsFetchReader();
  final DiscussionsFileSelector fileSelector = DiscussionsFileSelector();
  TextEditingController discussionTitleController = TextEditingController();
  TextEditingController discussionDescriptionController =
      TextEditingController();

  ValueNotifier<bool> isLoading =
      ValueNotifier(false); // Loading state notifier

  String filter = 'Trending'; // Default filter
  List<String> messages = ['Name', 'Description'];
  List<TextEditingController> controllersList = [];
  final FirebaseAuth auth = FirebaseAuth.instance;

  @override
  Widget build(BuildContext context) {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    controllersList = [
      discussionTitleController,
      discussionDescriptionController,
    ];

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(1.0),
              child: Container(
                height: 2.0,
                color: globals.bookBranchGreen,
              ),
            ),
            title: Text(
              'Project Discussions',
              style: TextStyle(
                color: globals.bookBranchGreen,
                fontWeight: FontWeight.w600,
                fontSize: 20,
                letterSpacing: 0.5,
              ),
            ),
            iconTheme: IconThemeData(color: globals.bookBranchGreen),
            actions: <Widget>[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: FilterChip(
                  label: Text(
                    'Trending',
                    style: TextStyle(
                      color: filter == 'Trending'
                          ? Colors.white
                          : globals.bookBranchGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  selected: filter == 'Trending',
                  onSelected: (bool selected) {
                    setState(() {
                      filter = 'Trending';
                    });
                  },
                  backgroundColor: Colors.white,
                  selectedColor: globals.bookBranchGreen,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: globals.bookBranchGreen,
                      width: 1,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 4.0, right: 12.0),
                child: FilterChip(
                  label: Text(
                    'Newest',
                    style: TextStyle(
                      color: filter == 'Newest'
                          ? Colors.white
                          : globals.bookBranchGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  selected: filter == 'Newest',
                  onSelected: (bool selected) {
                    setState(() {
                      filter = 'Newest';
                    });
                  },
                  backgroundColor: Colors.white,
                  selectedColor: globals.bookBranchGreen,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: globals.bookBranchGreen,
                      width: 1,
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Consumer(builder: (context, ref, child) {
            return FutureBuilder<List<List<dynamic>>?>(
              future: fetcher.fetchDiscussions(ref, filter),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text("Error: ${snapshot.error}"));
                } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  return ListView.builder(
                    itemCount: snapshot.data?.first.length ?? 0,
                    itemBuilder: (context, index) {
                      var discussion = snapshot.data!;
                      String formattedTime = DateFormat('yyyy-MM-dd – kk:mm')
                          .format(discussion[2][index].toDate());
                      return DiscussionCard(
                        title: discussion[0][index]!,
                        content: discussion[1][index]!,
                        date: formattedTime,
                        likes: fetcher
                            .fetchNumDiscussionLikes(discussion[3][index]!),
                        username: discussion[6][index]!,
                        discussionID: discussion[3][index]!,
                        onPressed: () {
                          ref.read(currentDiscussionIDProvider.notifier).state =
                              discussion[3][index]!;
                          ref
                              .read(currentDiscussionTitleProvider.notifier)
                              .state = discussion[0][index]!;
                          ref
                              .read(
                                  currentDiscussionDescriptionProvider.notifier)
                              .state = discussion[1][index]!;
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const DiscussionsCommentsWidgetReader(),
                            ),
                          );
                        },
                        onPressedLike: () {
                          logic.likeDiscussion(discussion[3][index]!,
                              auth.currentUser!.uid, ref);
                        },
                        onPressedDislike: () {
                          logic.dislikeDiscussion(discussion[3][index]!,
                              auth.currentUser!.uid, ref);
                        },
                      );
                    },
                  );
                } else {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Container(
                        width: double.infinity,
                        constraints: const BoxConstraints(maxWidth: 400),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(10),
                              blurRadius: 10,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                          border: Border.all(
                            color: globals.bookBranchGreen.withAlpha(50),
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'No Discussions Yet',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'There are no discussions in this section yet.',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Start a new discussion by tapping the + button below!',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade700,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: () {
                                // Create local variables for the popup
                                List<String> localAttachedFiles = [];
                                List<PlatformFile> localPlatformFiles = [];

                                fileSelector.createDiscussionPopup(
                                  context,
                                  messages,
                                  controllersList,
                                  localAttachedFiles,
                                  localPlatformFiles,
                                  ref,
                                  () async {
                                    // Same logic as in the FloatingActionButton
                                    String discussionTitle =
                                        controllersList[0].text.trim();
                                    String discussionDescription =
                                        controllersList[1].text.trim();
                                    int aggregateSize = localPlatformFiles
                                        .fold<int>(0, (s, f) => s + f.size);
                                    isLoading.value = true;

                                    try {
                                      final result = await FirebaseFunctions
                                          .instance
                                          .httpsCallable('hasEnoughStorage')
                                          .call({
                                        'fileSize': aggregateSize,
                                        'userId': uid,
                                      });

                                      if (!(result.data['success'] as bool)) {
                                        FocusManager.instance.primaryFocus
                                            ?.unfocus();
                                        SystemChannels.textInput
                                            .invokeMethod('TextInput.hide');
                                        if (context.mounted) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                'The owner of this project does not have enough '
                                                'available storage for the selected files.',
                                              ),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                        isLoading.value = false;
                                        return;
                                      }

                                      if (discussionTitle.isEmpty ||
                                          discussionDescription.isEmpty) {
                                        if (context.mounted) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                'Please enter both a title and a description.',
                                              ),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                        isLoading.value = false;
                                        return;
                                      }

                                      await logic.createNewDiscussion(
                                        discussionTitle,
                                        discussionDescription,
                                        localAttachedFiles,
                                        localPlatformFiles,
                                        ref,
                                      );

                                      FocusManager.instance.primaryFocus
                                          ?.unfocus();
                                      SystemChannels.textInput
                                          .invokeMethod('TextInput.hide');
                                      debugPrint(
                                          'New discussion created with associated files.');
                                    } catch (error) {
                                      FocusManager.instance.primaryFocus
                                          ?.unfocus();
                                      SystemChannels.textInput
                                          .invokeMethod('TextInput.hide');
                                      debugPrint('Error: $error');
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text('Error: $error')),
                                        );
                                      }
                                    }

                                    // Always reset loading state
                                    isLoading.value = false;
                                  },
                                );
                              },
                              icon: const Icon(Icons.add_comment),
                              label: const Text('Start a Discussion'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: globals.bookBranchGreen,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }
              },
            );
          }),
          floatingActionButton: Consumer(
            builder: (context, ref, child) {
              return FloatingActionButton(
                onPressed: () {
                  List<String> attachedFiles = [];
                  List<PlatformFile> platformFiles = [];

                  fileSelector.createDiscussionPopup(
                    context,
                    messages,
                    controllersList,
                    attachedFiles,
                    platformFiles,
                    ref,
                    () async {
                      String discussionTitle = controllersList[0].text.trim();
                      String discussionDescription =
                          controllersList[1].text.trim();
                      int aggregateSize =
                          platformFiles.fold<int>(0, (s, f) => s + f.size);

                      isLoading.value = true;

                      // Check storage quota
                      try {
                        final result = await FirebaseFunctions.instance
                            .httpsCallable('hasEnoughStorage')
                            .call({
                          'fileSize': aggregateSize,
                          'userId': uid,
                        });

                        if (!(result.data['success'] as bool)) {
                          // Hide keyboard
                          FocusManager.instance.primaryFocus?.unfocus();
                          SystemChannels.textInput
                              .invokeMethod('TextInput.hide');

                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'The owner of this project does not have enough '
                                  'available storage for the selected files.',
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                          isLoading.value = false;
                          return;
                        }

                        // Validate title & description
                        if (discussionTitle.isEmpty ||
                            discussionDescription.isEmpty) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Please enter both a title and a description.',
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                          isLoading.value = false;
                          return;
                        }

                        // Create the discussion
                        await logic.createNewDiscussion(
                          discussionTitle,
                          discussionDescription,
                          attachedFiles,
                          platformFiles,
                          ref,
                        );

                        // Hide keyboard on success
                        FocusManager.instance.primaryFocus?.unfocus();
                        SystemChannels.textInput.invokeMethod('TextInput.hide');
                        debugPrint(
                            'New discussion created with associated files.');
                      } catch (error) {
                        // Hide keyboard on error
                        FocusManager.instance.primaryFocus?.unfocus();
                        SystemChannels.textInput.invokeMethod('TextInput.hide');
                        debugPrint('Error: $error');

                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Error: $error')),
                          );
                        }
                      }

                      // Always reset loading state
                      isLoading.value = false;
                    },
                  );
                },
                backgroundColor: globals.bookBranchGreen,
                foregroundColor: Colors.white,
                child: const Icon(Icons.add),
              );
            },
          ),
        ),
        ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (context, loading, child) {
            if (loading) {
              return Container(
                color: Colors.black45,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            } else {
              return const SizedBox.shrink(); // Render nothing when not loading
            }
          },
        ),
      ],
    );
  }
}

class DiscussionCard extends ConsumerWidget {
  final String title;
  final String content;
  final String date;
  final Future<int> likes;
  final String username;
  final String discussionID;

  final VoidCallback onPressed;
  final VoidCallback onPressedLike;
  final VoidCallback onPressedDislike;

  const DiscussionCard({
    super.key,
    required this.title,
    required this.content,
    required this.date,
    required this.likes,
    required this.username,
    required this.discussionID,
    required this.onPressed,
    required this.onPressedLike,
    required this.onPressedDislike,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final likes = ref.watch(discussionLikesProvider(discussionID));

    return InkWell(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // Equivalent to 0.1 opacity
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: globals.bookBranchGreen.withAlpha(50),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                content,
                style: const TextStyle(fontSize: 14.0),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        date,
                        style:
                            const TextStyle(fontSize: 12.0, color: Colors.grey),
                      ),
                      Text(
                        username,
                        style:
                            const TextStyle(fontSize: 12.0, color: Colors.grey),
                      ),
                    ],
                  ),
                  Row(
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(Icons.thumb_up, size: 20),
                        onPressed: onPressedLike,
                        color: Colors.grey,
                      ),
                      likes.when(
                        data: (replies) => Text(
                          replies.toString(),
                          style: const TextStyle(color: Colors.grey),
                        ),
                        loading: () => const Text(
                          "Loading...",
                          style: TextStyle(color: Colors.grey),
                        ),
                        error: (error, stack) => const Text(
                          'Error',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.thumb_down, size: 20),
                        onPressed: onPressedDislike,
                        color: Colors.grey,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
