import 'package:flutter/material.dart';
import 'package:web_app/readerMode/database_screens/chapters_screen_reader.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_screens/discussions_screen_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/readerMode/main_logic/marketplace_logic.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/readerMode/database_screens/epublishing_screen_reader.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class SubscriptionDetailWidgetReader extends ConsumerWidget {
  static const String routeName = '/subscriptionDetailsMainReader';

  final UniversalWidgets universals = UniversalWidgets();
  final FetchDatabase fetch = FetchDatabase();
  final MarketPlaceLogic logic = MarketPlaceLogic();

  SubscriptionDetailWidgetReader({super.key});

  // Information text for each section
  final Map<String, String> sectionInfo = {
    "Trunk":
        "The Trunk section contains the main content of the project. Here you can find chapters, articles, and other primary content created by the author.",
    "Branches":
        "The Branches section contains supplementary content which can be paired with the main material in the Trunk section in order to expand on and complement it. This includes downloadable files, additional resources, and expanded content. \n\nFor example, an author might choose to name the sections within Branches the same as those within Trunks so that readers can easily find the corresponding content.",
    "Community":
        "The Community section is where you can engage with other readers and the author. Join discussions, share feedback, and connect with the project community."
  };

  void desc(WidgetRef ref) async {
    ref.read(projectDescriptionProvider.notifier).state =
        await fetch.fetchProjectDescription(ref.watch(currentProjectID));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    desc(ref);
    ref.watch(numberOfSubscribedProjectsByUserProvider);
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          ref.read(currentProjectName.notifier).state,
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: FutureBuilder<int>(
                future: Future.value(1),
                builder: (context, snapshot) {
                  return GridView.count(
                    crossAxisCount: 1,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    padding: const EdgeInsets.all(1.0),
                    childAspectRatio: 3 / 1.3,
                    children: [
                      universals.buildEnhancedColorfulCard(
                          context, "Trunk", sectionInfo["Trunk"]!, () {
                        ref.read(currentProjectSubSection.notifier).state =
                            "Content Reader";
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return ChaptersScreenReader();
                        }));
                      }),
                      universals.buildEnhancedColorfulCard(
                          context, "Branches", sectionInfo["Branches"]!, () {
                        ref.read(currentProjectSubSection.notifier).state =
                            "Content Reader";
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return EPublishingScreenReader();
                        }));
                      }),
                      universals.buildEnhancedColorfulCard(
                          context, "Community", sectionInfo["Community"]!, () {
                        ref.read(currentProjectSubSection.notifier).state =
                            "Community Reader";
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return const CommunityDiscussionsScreenReader();
                        }));
                      }),
                    ],
                  );
                },
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: universals.buildStyledButton(
              "Unsubscribe",
              true,
              () async {
                universals.showCustomPopupWithCheckbox(
                  context: context,
                  titleText: "Warning",
                  contentText:
                      "Are you sure you want to unsubscribe? You will lose access to this project.",
                  checkboxText: "I'm sure",
                  cancelButtonText: "Cancel",
                  actionButtonText: "Proceed",
                  onActionPressed: () async {
                    await logic.unsubscribeFromProject(
                        ref.watch(currentProjectID), ref);

                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                    // You can add navigation or other actions here if needed
                  },
                );
              },
              globals.bookBranchGreen,
              Colors.white,
              context,
              0.9, // 90% width
              borderRadius: 8.0,
              //icon: Icons.unsubscribe,
              elevation: 2.0,
            ),
          ),
        ],
      ),
    );
  }
}
