import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/readerMode/main_logic/marketplace_project_detail_logic_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/fetch_reader.dart';
import 'package:web_app/readerMode/main_logic/marketplace_logic.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/readerMode/main_screens/project_application_reader_screen.dart';
import 'package:web_app/readerMode/main_logic/questionnaire_logic_reader.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_provider_logic.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';

class MarketPlaceProjectDetailWidgetReader extends ConsumerWidget {
  MarketPlaceProjectDetailWidgetReader({super.key});

  final FetchDatabase fetch = FetchDatabase();
  final UniversalWidgets universalWidgets = UniversalWidgets();
  final FetchReader fetchReader = FetchReader();
  final MarketPlaceLogic logic = MarketPlaceLogic();
  final MarketPlaceProjectDetailLogicReader logicDetail =
      MarketPlaceProjectDetailLogicReader();
  final QuestionnaireLogicReader questionnaireLogicReader =
      QuestionnaireLogicReader();
  final QuestionnaireProviderLogic logicProvider = QuestionnaireProviderLogic();
  final FirebaseAuth auth = FirebaseAuth.instance;
  final bool isApply = true;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Fetching data from providers
    String thumbnailUrl = ref.watch(currentProjectThumbnailUrlProvider);
    String projectDescription = ref.watch(projectDescriptionReaderProvider);
    String projectName = ref.watch(currentProjectName);

    // Refresh subscription status (ignoring the result)
    // ignore: unused_result
    ref.refresh(isSubscribedProvider(ref.watch(currentProjectID)));

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          projectName,
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            _buildThumbnailImage(thumbnailUrl, context, projectName),
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Author name with icon
                    Flexible(
                      flex: 3,
                      child: Row(
                        children: [
                          const Icon(
                            Icons.person_outline,
                            size: 16,
                            color: Color.fromARGB(255, 44, 148, 44),
                          ),
                          const SizedBox(width: 6),
                          Flexible(
                            child: Text(
                              'Author: ${ref.watch(projectAuthorNameProvider)}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color.fromARGB(255, 90, 90, 90),
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Subscribe/Apply button
                    Flexible(
                      flex: 5, // Give more space to the button
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Consumer(
                          builder: (context, ref, _) {
                            return FutureBuilder<bool>(
                              future: logicDetail.isPrivateProject(
                                  ref.watch(currentProjectID), ref),
                              builder: (context, privateSnapshot) {
                                if (privateSnapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return const Center(
                                    child: SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<
                                                Color>(
                                            Color.fromARGB(255, 44, 148, 44)),
                                      ),
                                    ),
                                  );
                                } else if (privateSnapshot.hasError) {
                                  return Text(
                                      'Error: ${privateSnapshot.error}');
                                } else if (privateSnapshot.hasData) {
                                  return ref
                                      .watch(isSubscribedProvider(
                                          ref.watch(currentProjectID)))
                                      .when(
                                        data: (isSubscribed) {
                                          return privateSnapshot.data!
                                              ? applicationButton(
                                                  context, ref, 0.4)
                                              : subscriptionButton(context, ref,
                                                  isSubscribed, 0.4);
                                        },
                                        loading: () => const Center(
                                          child: SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Color.fromARGB(
                                                          255, 44, 148, 44)),
                                            ),
                                          ),
                                        ),
                                        error: (e, _) => Text('Error: $e'),
                                      );
                                } else {
                                  return const Text(
                                      "No data available for private status");
                                }
                              },
                            );
                          },
                        ),
                      ),
                    ),
                  ]),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: const [
                    BoxShadow(
                      color: Color.fromARGB(15, 0, 0, 0),
                      blurRadius: 8,
                      spreadRadius: 1,
                      offset: Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: const Color.fromARGB(15, 44, 148, 44),
                    width: 1,
                  ),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(
                          Icons.description_outlined,
                          color: Color.fromARGB(255, 44, 148, 44),
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Project Description',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color.fromARGB(255, 44, 148, 44),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      projectDescription,
                      style: const TextStyle(
                        fontSize: 16.0,
                        height: 1.5,
                        color: Color.fromARGB(220, 0, 0, 0),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThumbnailImage(
      String thumbnailUrl, BuildContext context, String projectName) {
    if (thumbnailUrl == 'default' || thumbnailUrl.isEmpty) {
      return Container(
        height: 200,
        width: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromARGB(25, 44, 148, 44), // 0.1 opacity
              Color.fromARGB(51, 44, 148, 44), // 0.2 opacity
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 80,
              color: globals.bookBranchGreen,
            ),
            const SizedBox(height: 16),
            Text(
              projectName,
              style: TextStyle(
                color: globals.bookBranchGreen,
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else {
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FullScreenMediaView(thumbnailUrl),
            ),
          );
        },
        child: Image.network(thumbnailUrl, height: 200, fit: BoxFit.cover),
      );
    }
  }

  Widget subscriptionButton(BuildContext context, WidgetRef ref,
      bool isSubscribed, double widthFactor) {
    String buttonText = isSubscribed ? 'Subscribed' : 'Subscribe';
    Color buttonColor = isSubscribed ? Colors.green : globals.bookBranchGreen;
    String projectName = ref.watch(currentProjectName);

    ref.watch(isSubscribedProvider(ref.watch(currentProjectID)));

    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        // Set minimum width to ensure button extends to the right
        constraints: BoxConstraints(
          minWidth: MediaQuery.of(context).size.width * 0.4, // Minimum width
        ),
        child: ElevatedButton(
          onPressed: () async {
            // Execute subscription/unsubscription logic based on current status
            if (isSubscribed) {
              await logic.unsubscribeFromProject(
                  ref.watch(currentProjectID), ref);

              // Show unsubscribe confirmation banner
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      "You have successfully unsubscribed from \"$projectName\".",
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    backgroundColor: globals.bookBranchGreen,
                    duration: const Duration(seconds: 3),
                    behavior: SnackBarBehavior.fixed,
                  ),
                );
              }
            } else {
              await logic.subscribeToProject(ref.watch(currentProjectID), ref);

              // Show subscribe confirmation banner
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      "You have successfully subscribed to \"$projectName\"!",
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    backgroundColor: globals.bookBranchGreen,
                    duration: const Duration(seconds: 3),
                    behavior: SnackBarBehavior.fixed,
                  ),
                );
              }
            }
            // Refresh the provider to update UI
            // Ignoring the result as we only need to trigger a refresh
            // ignore: unused_result
            ref.refresh(isSubscribedProvider(ref.watch(currentProjectID)));
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: buttonColor,
            foregroundColor: Colors.white,
            elevation: 2,
            padding: const EdgeInsets.symmetric(
                horizontal: 16.0, vertical: 10.0), // Increased padding
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.0),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize:
                MainAxisSize.min, // Allow row to shrink to fit content
            children: [
              Icon(
                isSubscribed
                    ? Icons.check_circle_outline
                    : Icons.add_circle_outline,
                size: 16,
              ),
              const SizedBox(width: 8), // Slightly increased spacing
              Flexible(
                child: Text(
                  buttonText,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.3,
                  ),
                  overflow:
                      TextOverflow.visible, // Ensure text doesn't get cut off
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget applicationButton(
      BuildContext context, WidgetRef ref, double widthFactor) {
    final User? user = auth.currentUser;
    final String uid = user?.uid ?? '';

    return FutureBuilder<bool>(
      future:
          logicProvider.hasAppliedToProject(uid, ref.watch(currentProjectID)),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: SizedBox(
              width: 30,
              height: 30,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                    Color.fromARGB(255, 44, 148, 44)),
              ),
            ),
          );
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (snapshot.hasData) {
          bool hasApplied = snapshot.data!;

          // Toggle text and color based on application status
          String buttonText =
              hasApplied ? 'Application Pending' : 'Apply to Project';
          Color buttonColor = hasApplied
              ? const Color.fromARGB(255, 230, 81, 0) // Orange for pending
              : const Color.fromARGB(255, 76, 175, 80); // Green for apply

          return Align(
            alignment: Alignment.centerRight,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              // Set minimum width to ensure button extends to the right
              constraints: BoxConstraints(
                minWidth:
                    MediaQuery.of(context).size.width * 0.4, // Minimum width
              ),
              child: ElevatedButton(
                onPressed: () {
                  // Add logic to handle Apply or Revoke based on hasApplied status
                  if (hasApplied) {
                    // Logic to revoke application
                  } else {
                    // Logic to apply
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ProjectApplicationReaderScreen(),
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: buttonColor,
                  foregroundColor: Colors.white,
                  elevation: 2,
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 10.0), // Increased padding
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize:
                      MainAxisSize.min, // Allow row to shrink to fit content
                  children: [
                    Icon(
                      hasApplied ? Icons.hourglass_top : Icons.edit_document,
                      size: 16,
                    ),
                    const SizedBox(width: 8), // Slightly increased spacing
                    Flexible(
                      child: Text(
                        buttonText,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.3,
                        ),
                        overflow: TextOverflow
                            .visible, // Ensure text doesn't get cut off
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          return const SizedBox(); // Handle the case when there is no data
        }
      },
    );
  }
}
