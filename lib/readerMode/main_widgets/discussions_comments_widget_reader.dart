import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:intl/intl.dart';
import 'package:web_app/readerMode/main_logic/discussions_logic_reader.dart';
import 'package:web_app/readerMode/main_logic/discussions_state_management_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/update_file_metadata.dart';
import 'package:tuple/tuple.dart';
import 'package:web_app/readerMode/main_logic/discussions_fetch_reader.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/readerMode/main_logic/discussion_comments_logic_reader.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';
import 'package:web_app/database/database_widgets/display_video_widget.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

UniversalWidgets universals = UniversalWidgets();
UpdateFileMetadata metadata = UpdateFileMetadata();
CommunityDiscussionsFetchReader communityDiscussionsFetchReader =
    CommunityDiscussionsFetchReader();
CommunityDiscussionsLogicReader communityDiscussionsLogicReader =
    CommunityDiscussionsLogicReader();

final TextEditingController _tagnameController = TextEditingController();
final TextEditingController _tagnameControllerNested = TextEditingController();

class DiscussionsCommentsWidgetReader extends ConsumerWidget {
  const DiscussionsCommentsWidgetReader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          ref.watch(currentDiscussionTitleProvider),
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: globals.bookBranchGreen),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: ListView(
        children: [
          DiscussionSummary(
            discussionID: ref.watch(currentDiscussionIDProvider),
          ),
          CommentSection(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Implement your add comment action here
          universals.showCustomPopupWithTextfield(context, "Post a Comment",
              "Write your message here", _tagnameController, () async {
            await communityDiscussionsLogicReader.postDiscussionComment(
                ref.watch(currentDiscussionIDProvider),
                _tagnameController.text,
                ref);
            _tagnameController.clear(); // Clear the text field after submission
            ref.watch(discussionCommentsProvider(ref.watch(
                currentDiscussionIDProvider))); // Refresh the number of comments displayed
          });
        },
        backgroundColor: globals.bookBranchGreen,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class DiscussionSummary extends ConsumerWidget {
  final String discussionID;
  final DiscussionCommentsLogicReader discussionCommentsLogicReader =
      DiscussionCommentsLogicReader();

  DiscussionSummary({super.key, required this.discussionID});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Map<String, IconData> fileTypeIcons = {
      'Image': Icons.image,
      'Video': Icons.videocam,
      'PDF': Icons.picture_as_pdf,
    };

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: globals.bookBranchGreen.withAlpha(70),
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              ref.watch(currentDiscussionTitleProvider),
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Text(
                ref.watch(currentDiscussionDescriptionProvider),
                style: TextStyle(
                  fontSize: 16,
                  height: 1.4,
                  color: Colors.grey.shade800,
                ),
              ),
            ),
            const SizedBox(height: 16),
            FutureBuilder<Map<String, String>>(
              future: discussionCommentsLogicReader
                  .fetchDiscussionFiles(discussionID),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (snapshot.hasError) {
                  return Text("Error: ${snapshot.error}");
                } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'Attachments',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      Wrap(
                        spacing: 12,
                        runSpacing: 12,
                        children: snapshot.data!.entries.map((entry) {
                          IconData icon =
                              fileTypeIcons[entry.value] ?? Icons.file_present;
                          String fileType = entry.value;

                          return GestureDetector(
                            onTap: () {
                              switch (entry.value) {
                                case 'Image':
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          FullScreenMediaView(entry.key),
                                    ),
                                  );
                                  break;
                                case 'PDF':
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          MobilePdfViewer(pdfUrl: entry.key),
                                    ),
                                  );
                                  break;
                                case 'Video':
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => DisplayVideoWidget(
                                          videoUrl: entry.key),
                                    ),
                                  );
                                  break;
                                default:
                                  // Handle unknown media type
                                  break;
                              }
                            },
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: globals.bookBranchGreen.withAlpha(30),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: globals.bookBranchGreen.withAlpha(50),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    icon,
                                    color: globals.bookBranchGreen,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    fileType,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: globals.bookBranchGreen,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  );
                } else {
                  return const Text("");
                }
              },
            )
          ],
        ),
      ),
    );
  }
}

class FullScreenMediaView extends StatelessWidget {
  final String imageUrl;

  const FullScreenMediaView(this.imageUrl, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        title: Text(
          'Image Viewer',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Center(
        child: Image.network(
          imageUrl,
          fit: BoxFit.contain,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                valueColor:
                    AlwaysStoppedAnimation<Color>(globals.bookBranchGreen),
              ),
            );
          },
        ),
      ),
    );
  }
}

class CommentSection extends ConsumerWidget {
  CommentSection({super.key});

  final List<dynamic> commentIDList = []; // List to store likes values
  final FirebaseAuth auth = FirebaseAuth.instance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<List<dynamic>>>(
      future: communityDiscussionsFetchReader.fetchDiscussionComments(
          ref, ref.watch(currentDiscussionIDProvider)),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(child: Text("Error: ${snapshot.error}"));
        } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
          final comments = snapshot.data!;
          return Column(
            children: List.generate(snapshot.data?.first.length ?? 0, (index) {
              String formattedTime = DateFormat('yyyy-MM-dd – kk:mm')
                  .format(comments[1][index].toDate()); // Format timestamp

              commentIDList.add(comments[4][index]); // Add commentID to list

              ref.watch(discussionCommentsProvider(ref.watch(
                  currentDiscussionIDProvider))); // Refresh the number of comments

              return CommentCard(
                author: comments[0][index],
                time: formattedTime,
                content: comments[2][index],
                likes: communityDiscussionsFetchReader.fetchNumberOfLikes(
                    ref.watch(currentDiscussionIDProvider), comments[4][index]),
                commentID: comments[4][index],
                fileUrl: ref.watch(chosenFileUrl),
                numReplies: communityDiscussionsFetchReader
                    .fetchNumberOfDiscussionReplies(
                        ref.watch(currentDiscussionIDProvider),
                        comments[4][index]),
                onPressed: () {
                  // Please note that a ref.refresh() is called within the postNestedFileComment() method itself
                  // Without this, the UI does not update appropriately
                  universals.showCustomPopupWithTextfield(
                      context,
                      "Post a Comment",
                      "Write your message here",
                      _tagnameControllerNested, () async {
                    await communityDiscussionsLogicReader
                        .postNestedDiscussionComment(
                            _tagnameControllerNested.text,
                            ref.watch(currentDiscussionIDProvider),
                            comments[4][index],
                            ref);
                    _tagnameControllerNested
                        .clear(); // Clear the text field after submission
                    if (context.mounted) {
                      showNestedComments(comments[4][index], context, ref);
                    }
                  });
                },
                onPressedViewReplies: () {
                  showNestedComments(comments[4][index], context, ref);
                },
                onPressedLike: () {
                  communityDiscussionsLogicReader.likeComment(
                      ref.watch(currentDiscussionIDProvider),
                      comments[4][index],
                      auth.currentUser!.uid,
                      ref);
                },
                onPressedDislike: () {
                  communityDiscussionsLogicReader.dislikeComment(
                      ref.watch(currentDiscussionIDProvider),
                      comments[4][index],
                      auth.currentUser!.uid,
                      ref);
                },
              );
            }),
          );
        } else {
          return Center(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
              child: Container(
                width: double.infinity,
                constraints: const BoxConstraints(maxWidth: 400),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: globals.bookBranchGreen.withAlpha(50),
                    width: 1,
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'No Comments Yet',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Be the first to share your thoughts on this discussion!',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () {
                        universals.showCustomPopupWithTextfield(
                            context,
                            "Post a Comment",
                            "Write your message here",
                            _tagnameController, () async {
                          await communityDiscussionsLogicReader
                              .postDiscussionComment(
                                  ref.watch(currentDiscussionIDProvider),
                                  _tagnameController.text,
                                  ref);
                          _tagnameController
                              .clear(); // Clear the text field after submission
                          ref.watch(discussionCommentsProvider(ref.watch(
                              currentDiscussionIDProvider))); // Refresh the number of comments displayed
                        });
                      },
                      icon: const Icon(Icons.comment),
                      label: const Text('Add a Comment'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: globals.bookBranchGreen,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }

  void showNestedComments(
      String parentCommentID, BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      backgroundColor: Colors.white, // Background color for the modal
      isScrollControlled: true,
      clipBehavior: Clip.antiAlias,
      builder: (BuildContext bc) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Replies',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                    color: Colors.grey.shade700,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Flexible(
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.7,
                ),
                child: FutureBuilder<List<dynamic>>(
                  future: communityDiscussionsFetchReader.fetchNestedComments(
                      ref.watch(currentDiscussionIDProvider),
                      parentCommentID,
                      ref),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (snapshot.hasError) {
                      return Text("Error: ${snapshot.error}");
                    } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                      return ListView.builder(
                        itemCount: snapshot.data!.length,
                        itemBuilder: (context, index) {
                          var nestedComment = snapshot.data![index];
                          return Consumer(
                            builder: (context, ref, child) {
                              final likes = ref.watch(
                                  discussionCommentsNestedLikesProvider(Tuple3(
                                      ref.watch(currentDiscussionIDProvider),
                                      parentCommentID,
                                      nestedComment['commentID'])));
                              return Container(
                                margin: const EdgeInsets.symmetric(
                                    vertical: 8.0, horizontal: 12.0),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(10),
                                      spreadRadius: 1,
                                      blurRadius: 2,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                  border: Border.all(
                                    color:
                                        globals.bookBranchGreen.withAlpha(30),
                                    width: 1,
                                  ),
                                ),
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: 28,
                                              height: 28,
                                              decoration: BoxDecoration(
                                                color: globals.bookBranchGreen
                                                    .withAlpha(15),
                                                shape: BoxShape.circle,
                                              ),
                                              child: Center(
                                                child: Text(
                                                  nestedComment['displayName']
                                                          .isNotEmpty
                                                      ? nestedComment[
                                                              'displayName'][0]
                                                          .toUpperCase()
                                                      : '?',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.bold,
                                                    color:
                                                        globals.bookBranchGreen,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              nestedComment['displayName'],
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.thumb_up,
                                                  size: 16),
                                              onPressed: () {
                                                communityDiscussionsLogicReader
                                                    .likeNestedComment(
                                                        ref.watch(
                                                            currentDiscussionIDProvider),
                                                        parentCommentID,
                                                        nestedComment[
                                                            'commentID'],
                                                        auth.currentUser!.uid,
                                                        ref);
                                              },
                                              color: Colors.grey.shade600,
                                              padding: EdgeInsets.zero,
                                              constraints:
                                                  const BoxConstraints(),
                                            ),
                                            const SizedBox(width: 4),
                                            likes.when(
                                              data: (likesCount) => Text(
                                                likesCount.toString(),
                                                style: TextStyle(
                                                  color: Colors.grey.shade600,
                                                  fontSize: 12,
                                                ),
                                              ),
                                              loading: () => Text(
                                                '...',
                                                style: TextStyle(
                                                  color: Colors.grey.shade600,
                                                  fontSize: 12,
                                                ),
                                              ),
                                              error: (error, stack) => Text(
                                                '0',
                                                style: TextStyle(
                                                  color: Colors.grey.shade600,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            IconButton(
                                              icon: const Icon(Icons.thumb_down,
                                                  size: 16),
                                              onPressed: () {
                                                communityDiscussionsLogicReader
                                                    .dislikeNestedComment(
                                                        ref.watch(
                                                            currentDiscussionIDProvider),
                                                        parentCommentID,
                                                        nestedComment[
                                                            'commentID'],
                                                        auth.currentUser!.uid,
                                                        ref);
                                              },
                                              color: Colors.grey.shade600,
                                              padding: EdgeInsets.zero,
                                              constraints:
                                                  const BoxConstraints(),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade50,
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                          color: Colors.grey.shade200,
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        nestedComment['comment'],
                                        style: TextStyle(
                                          fontSize: 14.0,
                                          color: Colors.grey.shade800,
                                          height: 1.3,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                      );
                    } else {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.question_answer_outlined,
                                size: 48,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                "No replies yet",
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                "Be the first to reply to this comment!",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class CommentCard extends ConsumerWidget {
  final String author;
  final String time;
  final String content;
  final Future<int> likes;
  final List<Widget> replies; // This will hold nested CommentCards
  final Future<int> numReplies;
  final String commentID;
  final String fileUrl;

  final VoidCallback onPressed;
  final VoidCallback onPressedViewReplies;
  final VoidCallback onPressedLike;
  final VoidCallback onPressedDislike;

  const CommentCard({
    super.key,
    required this.author,
    required this.time,
    required this.content,
    required this.likes,
    required this.commentID,
    required this.fileUrl,
    required this.onPressed,
    required this.onPressedViewReplies,
    required this.onPressedLike,
    required this.onPressedDislike,
    required this.numReplies,
    this.replies = const [],
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final numReplies = ref.watch(discussionCommentsRepliesProvider(
        Tuple2(ref.watch(currentDiscussionIDProvider), commentID)));

    final likes = ref.watch(discussionCommentsLikesProvider(
        Tuple2(ref.watch(currentDiscussionIDProvider), commentID)));

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: globals.bookBranchGreen.withAlpha(40),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: globals.bookBranchGreen.withAlpha(20),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          author.isNotEmpty ? author[0].toUpperCase() : '?',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: globals.bookBranchGreen,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          author,
                          style: const TextStyle(
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          time,
                          style: TextStyle(
                            fontSize: 12.0,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.thumb_up, size: 18),
                      onPressed: onPressedLike,
                      color: Colors.grey.shade600,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    const SizedBox(width: 4),
                    likes.when(
                      data: (replies) => Text(
                        replies.toString(),
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      loading: () => Text(
                        "...",
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      error: (error, stack) => Text(
                        '0',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    ),
                    const SizedBox(width: 12),
                    IconButton(
                      icon: const Icon(Icons.thumb_down, size: 18),
                      onPressed: onPressedDislike,
                      color: Colors.grey.shade600,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Text(
                content,
                style: TextStyle(
                  fontSize: 15.0,
                  color: Colors.grey.shade800,
                  height: 1.4,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                InkWell(
                  onTap: onPressed,
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: globals.bookBranchGreen.withAlpha(15),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: globals.bookBranchGreen.withAlpha(30),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.reply,
                          size: 16,
                          color: globals.bookBranchGreen,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Reply',
                          style: TextStyle(
                            color: globals.bookBranchGreen,
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                InkWell(
                  onTap: onPressedViewReplies,
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.question_answer,
                          size: 16,
                          color: Colors.grey.shade700,
                        ),
                        const SizedBox(width: 4),
                        numReplies.when(
                          data: (count) => Text(
                            count > 0 ? 'View $count replies' : 'No replies',
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          loading: () => Text(
                            'Loading...',
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          error: (error, stack) => Text(
                            'View replies',
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
