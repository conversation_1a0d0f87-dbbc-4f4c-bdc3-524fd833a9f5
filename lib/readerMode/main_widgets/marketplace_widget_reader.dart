import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/fetch_reader.dart';
import 'package:web_app/readerMode/main_logic/marketplace_logic.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_screens/marketplace_project_detail_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/marketplace_search_screen_reader.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class MarketPlaceWidgetReader extends ConsumerStatefulWidget {
  @override
  _MarketPlaceWidgetReaderState createState() =>
      _MarketPlaceWidgetReaderState();
}

class _MarketPlaceWidgetReaderState
    extends ConsumerState<MarketPlaceWidgetReader> {
  List<Map<String, dynamic>> projects = [];
  List<Map<String, dynamic>> thumbnails = [];
  Map<String, dynamic> users = {};
  bool isLoading = true; // State variable to manage loading state
  bool hasMore =
      true; // State variable to determine if there are more projects to load
  String? lastProjectId; // State variable to manage pagination

  final MarketPlaceLogic fetch = MarketPlaceLogic();
  final FetchReader fetchReader = FetchReader();
  final FirebaseAuth auth = FirebaseAuth.instance;

  // Helper method to truncate text with ellipsis
  String _truncateWithEllipsis(int cutoff, String myString) {
    if (myString.length <= cutoff) {
      return myString;
    }
    return '${myString.substring(0, cutoff)}...';
  }

  @override
  void initState() {
    super.initState();
    _fetchProjects(); // Fetch initial projects when the widget is first created
  }

  Future<void> _fetchProjects() async {
    setState(() {
      isLoading = true; // Set loading state to true before fetching data
    });

    // Fetch projects, thumbnails, and user data
    final result = await Future.wait([
      fetchReader.fetchAllPublicProjects(
          startAfter: lastProjectId), // Fetch projects with pagination
      fetchReader.fetchAllPublicProjectThumbnails(),
      fetchReader.fetchAllUserData(auth.currentUser!.uid)
    ]);

    List<Map<String, dynamic>> newProjects =
        List<Map<String, dynamic>>.from(result[0] as List);
    List<Map<String, dynamic>> newThumbnails =
        List<Map<String, dynamic>>.from(result[1] as List);
    Map<String, dynamic> newUsers = Map<String, dynamic>.from(result[2] as Map);

    setState(() {
      projects.addAll(newProjects); // Add new projects to the existing list
      thumbnails = newThumbnails;
      users = newUsers;
      isLoading = false; // Set loading state to false after fetching data
      hasMore =
          newProjects.length == 10; // Check if there are more projects to load
      lastProjectId = newProjects.isNotEmpty
          ? newProjects.last['Project ID']
          : null; // Update pagination state
    });
  }

  @override
  Widget build(BuildContext context) {
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: MarketPlaceScreenReader.routeName)
          : NavDrawerBasic(currentRoute: MarketPlaceScreenReader.routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Marketplace',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        actions: [
          IconButton(
            icon: Icon(
              Icons.search,
              color: globals.bookBranchGreen,
            ),
            onPressed: () {
              // Add search functionality here
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return MarketPlaceSearchScreenReader();
              }));
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 2.0, left: 2, right: 2, bottom: 2),
        child: kIsWeb
            ? displayMarketPlace(ref, context)
            : displayMarketPlaceMobile(ref, context),
      ),
    );
  }

  Widget displayMarketPlaceMobile(WidgetRef ref, BuildContext context) {
    return isLoading &&
            projects.isEmpty // Show loading indicator if initially loading
        ? const Center(child: CircularProgressIndicator())
        : ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: projects.length +
                (hasMore
                    ? 1
                    : 0), // Adjust item count to include 'Load More Projects' button if needed
            itemBuilder: (context, i) {
              if (i == projects.length && hasMore) {
                // Render the 'Load More Projects' button
                return Center(
                  child: ElevatedButton(
                    onPressed:
                        _fetchProjects, // Fetch more projects when button is pressed
                    style: ElevatedButton.styleFrom(
                      backgroundColor: globals.bookBranchGreen,
                      foregroundColor: Colors.white,
                      elevation: 2,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Load More Projects',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                );
              } else {
                var project = projects[i];
                String thumbnailUrl = thumbnails.firstWhere(
                        (thumbnail) =>
                            thumbnail['Project ID'] == project['Project ID'],
                        orElse: () => {'thumbnail': 'default'})['thumbnail'] ??
                    'default'; // This ensures thumbnailUrl is never null.

                bool isDefaultThumbnail = thumbnailUrl == 'default';

                return isDefaultThumbnail
                    ? defaultProjectButton(ref, context, project, users)
                    : imageProjectButton(
                        ref, context, project, users, thumbnailUrl);
              }
            },
            physics: const BouncingScrollPhysics(),
          );
  }

  Widget displayMarketPlace(
    WidgetRef ref,
    BuildContext context,
  ) {
    return FutureBuilder<List<dynamic>>(
      future: Future.wait([
        fetchReader.fetchAllPublicProjects(),
        fetchReader.fetchAllPublicProjectThumbnails(),
        fetchReader.fetchAllUserData(auth.currentUser!.uid)
      ]),
      builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Text('No data available.');
        } else {
          List<Map<String, dynamic>> projects = snapshot.data![0];
          List<Map<String, dynamic>> thumbnails = snapshot.data![1];
          Map<String, dynamic> users = snapshot.data![2];

          return GridView.builder(
            padding: const EdgeInsets.all(8),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 15,
              mainAxisSpacing: 15,
              mainAxisExtent:
                  250, // Fixed height that accommodates most descriptions
            ),
            itemCount: projects.length,
            itemBuilder: (context, i) {
              var project = projects[i];
              String thumbnailUrl = thumbnails.firstWhere(
                      (thumbnail) =>
                          thumbnail['Project ID'] == project['Project ID'],
                      orElse: () => {'thumbnail': 'default'})['thumbnail'] ??
                  'default'; // This ensures thumbnailUrl is never null.

              bool isDefaultThumbnail = thumbnailUrl == 'default';

              return isDefaultThumbnail
                  ? defaultProjectButton(ref, context, project, users)
                  : imageProjectButton(
                      ref, context, project, users, thumbnailUrl);
            },
            physics: const BouncingScrollPhysics(),
          );
        }
      },
    );
  }

  Widget defaultProjectButton(WidgetRef ref, BuildContext context,
      Map<String, dynamic> project, Map<String, dynamic> users) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(
        25, 44, 148, 44); // Using ARGB values instead of withOpacity

    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(
                25, 0, 0, 0), // Using ARGB values instead of withOpacity
            spreadRadius: 1,
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 100),
      child: InkWell(
        onTap: () {
          ref.read(currentProjectID.notifier).state = project['Project ID'];
          ref.read(currentProjectName.notifier).state = project['Project Name'];
          ref.read(projectDescriptionReaderProvider.notifier).state =
              project['Description'];
          ref.read(currentProjectThumbnailUrlProvider.notifier).state =
              project['thumbnail'];
          ref.read(projectAuthorNameProvider.notifier).state =
              project['Display Name'] ?? 'Anonymous';
          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return MarketPlaceProjectDetailScreenReader();
          }));
        },
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 8,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148,
                          44), // Using ARGB values instead of withOpacity
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 8,
              right: 0,
              child: Container(
                height: 40,
                width: 40,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(40),
                  ),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Row for icon and project name
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project icon placeholder
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: lightGreen,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.book_outlined,
                          color: bookBranchGreen,
                          size: 25,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Project name and author in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Project name
                            Text(
                              project['Project Name'],
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // Author info with icon
                            Row(
                              children: [
                                const Icon(
                                  Icons.person_outline,
                                  size: 14,
                                  color: bookBranchGreen,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Author: ${project['Display Name']}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            // Project description with character limit
                            if (project['Description'] != null)
                              Text(
                                _truncateWithEllipsis(
                                    300, project['Description']),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  height: 1.4,
                                ),
                                maxLines: 10,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget imageProjectButton(
      WidgetRef ref,
      BuildContext context,
      Map<String, dynamic> project,
      Map<String, dynamic> users,
      String thumbnailUrl) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(
        25, 44, 148, 44); // Using ARGB values instead of withOpacity

    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(
                25, 0, 0, 0), // Using ARGB values instead of withOpacity
            spreadRadius: 1,
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 100),
      child: InkWell(
        onTap: () {
          ref.read(currentProjectID.notifier).state = project['Project ID'];
          ref.read(currentProjectName.notifier).state = project['Project Name'];
          ref.read(projectDescriptionReaderProvider.notifier).state =
              project['Description'];
          ref.read(currentProjectThumbnailUrlProvider.notifier).state =
              project['thumbnail'];
          ref.read(projectAuthorNameProvider.notifier).state =
              project['Display Name'] ?? 'Anonymous';
          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return MarketPlaceProjectDetailScreenReader();
          }));
        },
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 8,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148, 44)
                    ], // Using ARGB values instead of withOpacity
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 8,
              right: 0,
              child: Container(
                height: 40,
                width: 40,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(40),
                  ),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Row for thumbnail and project name
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project thumbnail
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: thumbnailUrl != 'default'
                            ? Image.network(
                                thumbnailUrl,
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                              )
                            : Container(
                                width: 50,
                                height: 50,
                                color: lightGreen,
                                child: const Icon(
                                  Icons.book_outlined,
                                  color: bookBranchGreen,
                                  size: 25,
                                ),
                              ),
                      ),
                      const SizedBox(width: 12),
                      // Project name and author in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Project name
                            Text(
                              project['Project Name'],
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // Author info with icon
                            Row(
                              children: [
                                const Icon(
                                  Icons.person_outline,
                                  size: 14,
                                  color: bookBranchGreen,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Author: ${project['Display Name']}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            // Project description with character limit
                            if (project['Description'] != null)
                              Text(
                                _truncateWithEllipsis(
                                    300, project['Description']),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  height: 1.4,
                                ),
                                maxLines: 10,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
