import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/main_screens/subscription_detail_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/subscriptions_screen_reader.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/fetch_reader.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/readerMode/main_logic/main_logic_fetch/subscriptions_logic_fetch.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/readerMode/main_screens/subscriptions_search_screen_reader.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';

class SubscriptionsWidgetReader extends ConsumerWidget {
  SubscriptionsWidgetReader({super.key});

  final FetchReader fetchReader = FetchReader();
  final SubscriptionsLogicFetch subscriptionsLogicFetch =
      SubscriptionsLogicFetch();

  final FirebaseAuth auth = FirebaseAuth.instance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(numberOfSubscribedProjectsByUserProvider);
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: SubscriptionsScreenReader.routeName)
          : NavDrawerBasic(currentRoute: SubscriptionsScreenReader.routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'My Subscriptions',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        actions: [
          IconButton(
            icon: Icon(
              Icons.search,
              color: globals.bookBranchGreen,
            ),
            tooltip: 'Search subscriptions',
            onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return SubscriptionsSearchScreenReader();
              }));
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 2.0, left: 2, right: 2, bottom: 2),
        child: displaySubscriptionsMobile(
            ref, context), // Direct call without FutureBuilder
      ),
    );
  }

  Widget displaySubscriptionsMobile(WidgetRef ref, BuildContext context) {
    final User? user = auth.currentUser;
    final String? uid = user?.uid;

    Future<List<Map<String, dynamic>>> projectDataFuture =
        subscriptionsLogicFetch
            .fetchSubscribedProjectIds(uid!)
            .then((List<String> projectIds) {
      return subscriptionsLogicFetch.fetchSubscribedProjectsData(projectIds);
    }).then((List<Map<String, String>> thumbnails) {
      // Here you might want to fetch more project details to pass to imageProjectButton
      // For now, we'll proceed with assuming thumbnails include necessary project details
      return thumbnails;
    });

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: projectDataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Container(
                width: double.infinity,
                constraints: const BoxConstraints(maxWidth: 400),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: globals.bookBranchGreen.withAlpha(50),
                    width: 1,
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'No Subscriptions Yet',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'You are not subscribed to any projects yet.',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Discover projects in the Marketplace to get started!',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),

                    // Action button
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context)
                            .pushNamed(MarketPlaceScreenReader.routeName);
                      },
                      icon: const Icon(Icons.shopping_bag_outlined),
                      label: const Text('Browse Marketplace'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: globals.bookBranchGreen,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          // Use a GridView or ListView.builder based on your UI design
          return GridView.builder(
            padding: const EdgeInsets.all(0),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 1, // Only one item per row
              crossAxisSpacing: 15,
              mainAxisSpacing: 15,
              childAspectRatio:
                  3 / 1.3, // Reduced height since there's no description
            ),
            itemCount: snapshot.data!.length,
            itemBuilder: (context, index) {
              var project = snapshot.data![index];
              String thumbnailUrl = project['thumbnail'];

              // Decide which widget to use based on the thumbnailUrl
              return thumbnailUrl == 'default'
                  ? defaultProjectButton(ref, context, project,
                      thumbnailUrl) // Assuming you have a defaultProjectButton similar to imageProjectButton
                  : imageProjectButton(ref, context, project, thumbnailUrl);
            },
            physics: const BouncingScrollPhysics(),
          );
        }
      },
    );
  }

  Widget defaultProjectButton(WidgetRef ref, BuildContext context,
      Map<String, dynamic> project, String thumbnailUrl) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(25, 0, 0, 0),
            spreadRadius: 1,
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      width: double.infinity,
      child: InkWell(
        onTap: () {
          ref.read(currentProjectID.notifier).state = project['Project ID'];
          ref.read(currentProjectName.notifier).state = project['Project Name'];
          ref.read(projectDescriptionReaderProvider.notifier).state =
              project['Description'];
          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return const SubscriptionDetailScreenReader();
          }));
        },
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 8,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148, 44),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 8,
              right: 0,
              child: Container(
                height: 30,
                width: 30,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                  ),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Row for icon and project name
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project icon placeholder
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: lightGreen,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.book_outlined,
                          color: bookBranchGreen,
                          size: 25,
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Project name and author in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Project name
                            Text(
                              project['Project Name'],
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // Author info with icon
                            Row(
                              children: [
                                const Icon(
                                  Icons.person_outline,
                                  size: 14,
                                  color: bookBranchGreen,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Author: ${project['Display Name']}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Subscription indicator
            const Positioned(
              top: 12,
              right: 12,
              child: Icon(
                Icons.check_circle,
                color: bookBranchGreen,
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget imageProjectButton(WidgetRef ref, BuildContext context,
      Map<String, dynamic> project, String thumbnailUrl) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(25, 0, 0, 0),
            spreadRadius: 1,
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: bookBranchGreen,
          width: 1.5,
        ),
      ),
      width: double.infinity,
      child: InkWell(
        onTap: () {
          ref.read(currentProjectID.notifier).state = project['Project ID'];
          ref.read(currentProjectName.notifier).state = project['Project Name'];
          ref.read(projectDescriptionReaderProvider.notifier).state =
              project['Description'];
          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return const SubscriptionDetailScreenReader();
          }));
        },
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 8,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148, 44),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 8,
              right: 0,
              child: Container(
                height: 30,
                width: 30,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                  ),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Row for thumbnail and project name
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project thumbnail
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: thumbnailUrl != 'default'
                            ? Image.network(
                                thumbnailUrl,
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                              )
                            : Container(
                                width: 50,
                                height: 50,
                                color: lightGreen,
                                child: const Icon(
                                  Icons.book_outlined,
                                  color: bookBranchGreen,
                                  size: 25,
                                ),
                              ),
                      ),
                      const SizedBox(width: 12),
                      // Project name and author in a column
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Project name
                            Text(
                              project['Project Name'],
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w600,
                                color: Color.fromARGB(204, 0, 0, 0),
                                letterSpacing: 0.3,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),
                            // Author info with icon
                            Row(
                              children: [
                                const Icon(
                                  Icons.person_outline,
                                  size: 14,
                                  color: bookBranchGreen,
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    'Author: ${project['Display Name']}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Subscription indicator
            const Positioned(
              top: 12,
              right: 12,
              child: Icon(
                Icons.check_circle,
                color: bookBranchGreen,
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
