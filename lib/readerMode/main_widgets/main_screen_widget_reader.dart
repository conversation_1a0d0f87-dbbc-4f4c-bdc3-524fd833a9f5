import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/main_logic/main_screen_logic_reader.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/subscriptions_screen_reader.dart';

class MainScreenWidgetReader extends ConsumerStatefulWidget {
  const MainScreenWidgetReader({super.key});

  @override
  MainScreenWidgetReaderState createState() => MainScreenWidgetReaderState();
}

class MainScreenWidgetReaderState
    extends ConsumerState<MainScreenWidgetReader> {
  final FirebaseAuth auth = FirebaseAuth.instance;
  MainScreenLogicReader logic = MainScreenLogicReader();

  Widget _buildEmptyNotificationsView(BuildContext context, WidgetRef ref) {
    final displayName = ref.watch(displayNameProvider);
    final firstName = displayName.split(' ')[0];

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome header
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 16, bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, $firstName',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Here\'s what\'s happening:',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),

            // Empty notifications card
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: globals.bookBranchGreen.withAlpha(50),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  // Card header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: globals.bookBranchGreen.withAlpha(15),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(15),
                        topRight: Radius.circular(15),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.notifications_none_rounded,
                          color: globals.bookBranchGreen,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Notifications',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Card content
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        // Illustration
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.mark_email_unread_outlined,
                            size: 40,
                            color: Colors.grey.shade500,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Message
                        const Text(
                          'No Notifications Yet',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'You have not received any notifications yet. They will appear here when you do.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Notifications will keep you updated on comments and activity related to your subscriptions.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Quick actions section
            const Padding(
              padding: EdgeInsets.only(left: 8.0, bottom: 16.0),
              child: Text(
                'QUICK ACTIONS',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                  color: Colors.grey,
                ),
              ),
            ),

            // Action cards row
            Row(
              children: [
                // Marketplace card
                Expanded(
                  child: _buildActionCard(
                    context,
                    'Explore Marketplace',
                    Icons.shopping_bag_outlined,
                    () => Navigator.pushNamed(
                        context, MarketPlaceScreenReader.routeName),
                  ),
                ),
                const SizedBox(width: 16),
                // Subscriptions card
                Expanded(
                  child: _buildActionCard(
                    context,
                    'My Subscriptions',
                    Icons.subscriptions_outlined,
                    () => Navigator.pushNamed(
                        context, SubscriptionsScreenReader.routeName),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 6,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: globals.bookBranchGreen.withAlpha(15),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: globals.bookBranchGreen,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> fetchNotificationStatus() async {
    try {
      String? uid = FirebaseAuth.instance.currentUser?.uid;

      DocumentSnapshot snapshot =
          await FirebaseFirestore.instance.collection("users").doc(uid).get();

      if (snapshot.exists) {
        // Cast snapshot data to a map and access the 'Notifications' field
        Map<String, dynamic> data = snapshot.data() as Map<String, dynamic>;
        return data['Notifications'] ?? false;
      }
    } catch (e) {
      // Error fetching notification status
    }
    return false;
  }

  Future<void> deleteNotification(String commentID) async {
    try {
      await FirebaseFirestore.instance
          .collection('notifications')
          .doc(commentID)
          .delete();
      // Notification deleted successfully
    } catch (e) {
      // Error deleting notification
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = auth.currentUser;

    return Scaffold(
      body: FutureBuilder<bool>(
        future: fetchNotificationStatus(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (snapshot.hasData && snapshot.data == true) {
            return FutureBuilder<List<Map<String, dynamic>>>(
              future: logic.fetchAllUserDiscussionComments(user!.uid, ref),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else {
                  List<Map<String, dynamic>> commentsData = snapshot.data ?? [];

                  if (commentsData.isEmpty) {
                    return _buildEmptyNotificationsView(context, ref);
                  } else {
                    return _buildNotificationsView(context, ref, commentsData);
                  }
                }
              },
            );
          } else {
            return _buildNotificationsDisabledView(context, ref);
          }
        },
      ),
    );
  }

  Widget _buildNotificationsDisabledView(BuildContext context, WidgetRef ref) {
    final displayName = ref.watch(displayNameProvider);
    final firstName = displayName.split(' ')[0];

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome header
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 16, bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, $firstName',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Here\'s what\'s happening:',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),

            // Notifications disabled card
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: globals.bookBranchGreen.withAlpha(50),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  // Card header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: globals.bookBranchGreen.withAlpha(15),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(15),
                        topRight: Radius.circular(15),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.notifications_off_outlined,
                          color: globals.bookBranchGreen,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Notifications',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Card content
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        // Illustration
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.notifications_paused_outlined,
                            size: 40,
                            color: Colors.grey.shade500,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Message
                        const Text(
                          'Notifications are Disabled',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'You currently have notifications turned off. Enable them to stay updated on comments and activity related to your subscriptions.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),

                        // Enable button
                        ElevatedButton.icon(
                          onPressed: () async {
                            // Update user's notification preference in Firestore
                            try {
                              String? uid =
                                  FirebaseAuth.instance.currentUser?.uid;
                              if (uid != null) {
                                await FirebaseFirestore.instance
                                    .collection("users")
                                    .doc(uid)
                                    .update({'Notifications': true});

                                // Force refresh
                                setState(() {});
                              }
                            } catch (e) {
                              // Handle error silently in production
                              // In a real app, this would use a logging framework
                            }
                          },
                          icon: const Icon(Icons.notifications_active_outlined),
                          label: const Text('Enable Notifications'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: globals.bookBranchGreen,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Quick actions section
            const Padding(
              padding: EdgeInsets.only(left: 8.0, bottom: 16.0),
              child: Text(
                'QUICK ACTIONS',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                  color: Colors.grey,
                ),
              ),
            ),

            // Action cards row
            Row(
              children: [
                // Marketplace card
                Expanded(
                  child: _buildActionCard(
                    context,
                    'Explore Marketplace',
                    Icons.shopping_bag_outlined,
                    () => Navigator.pushNamed(
                        context, MarketPlaceScreenReader.routeName),
                  ),
                ),
                const SizedBox(width: 16),
                // Subscriptions card
                Expanded(
                  child: _buildActionCard(
                    context,
                    'My Subscriptions',
                    Icons.subscriptions_outlined,
                    () => Navigator.pushNamed(
                        context, SubscriptionsScreenReader.routeName),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsView(BuildContext context, WidgetRef ref,
      List<Map<String, dynamic>> commentsData) {
    final displayName = ref.watch(displayNameProvider);
    final firstName = displayName.split(' ')[0];

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome header
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 16, bottom: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, $firstName',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Here\'s what\'s happening:',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),

            // Notifications card
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: globals.bookBranchGreen.withAlpha(50),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  // Card header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: globals.bookBranchGreen.withAlpha(15),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(15),
                        topRight: Radius.circular(15),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.notifications_active_rounded,
                              color: globals.bookBranchGreen,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Notifications',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: globals.bookBranchGreen,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${commentsData.length}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Notification list
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(0),
                    itemCount: commentsData.length,
                    itemBuilder: (context, index) {
                      var commentData = commentsData[index];
                      var displayName = commentData['displayName'];
                      var timestamp = commentData['timestamp'].toDate();
                      var comment = commentData['comment'];
                      var discussionTitle = commentData['discussionTitle'];
                      var commentID = commentData['commentID'];

                      // Format the date nicely
                      final now = DateTime.now();
                      final difference = now.difference(timestamp);
                      String timeAgo;

                      if (difference.inDays > 0) {
                        timeAgo = '${difference.inDays}d ago';
                      } else if (difference.inHours > 0) {
                        timeAgo = '${difference.inHours}h ago';
                      } else if (difference.inMinutes > 0) {
                        timeAgo = '${difference.inMinutes}m ago';
                      } else {
                        timeAgo = 'Just now';
                      }

                      return Dismissible(
                        key: Key(commentID),
                        direction: DismissDirection.endToStart,
                        onDismissed: (direction) async {
                          await deleteNotification(commentID);
                          setState(() {
                            commentsData.removeAt(index);
                          });
                        },
                        background: Container(
                          color: Colors.red.shade100,
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Icon(
                            Icons.delete_outline,
                            color: Colors.red.shade700,
                          ),
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: index == commentsData.length - 1
                                    ? Colors.transparent
                                    : Colors.grey.shade200,
                                width: 1,
                              ),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // User avatar
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color:
                                        globals.bookBranchGreen.withAlpha(15),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      displayName.isNotEmpty
                                          ? displayName[0].toUpperCase()
                                          : '?',
                                      style: TextStyle(
                                        color: globals.bookBranchGreen,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),

                                // Notification content
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              displayName,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 15,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          Text(
                                            timeAgo,
                                            style: TextStyle(
                                              color: Colors.grey.shade600,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Responded to your discussion "$discussionTitle"',
                                        style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 13,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade50,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                            color: Colors.grey.shade200,
                                          ),
                                        ),
                                        child: Text(
                                          comment,
                                          style: const TextStyle(
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Delete button
                                GestureDetector(
                                  onTap: () async {
                                    await deleteNotification(commentID);
                                    setState(() {
                                      commentsData.removeAt(index);
                                    });
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.grey.shade400,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // Quick actions section
            const Padding(
              padding: EdgeInsets.only(left: 8.0, bottom: 16.0),
              child: Text(
                'QUICK ACTIONS',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                  color: Colors.grey,
                ),
              ),
            ),

            // Action cards row
            Row(
              children: [
                // Marketplace card
                Expanded(
                  child: _buildActionCard(
                    context,
                    'Explore Marketplace',
                    Icons.shopping_bag_outlined,
                    () => Navigator.pushNamed(
                        context, MarketPlaceScreenReader.routeName),
                  ),
                ),
                const SizedBox(width: 16),
                // Subscriptions card
                Expanded(
                  child: _buildActionCard(
                    context,
                    'My Subscriptions',
                    Icons.subscriptions_outlined,
                    () => Navigator.pushNamed(
                        context, SubscriptionsScreenReader.routeName),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
