import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';
import 'package:web_app/readerMode/main_logic/questionnaire_logic_reader.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/user_metadata_fetch.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/ordered_item_logic.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class ProjectApplicationWidgetReader extends ConsumerWidget {
  // Helper method to build a card container
  Widget _buildCard(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  final TextEditingController controller = TextEditingController();
  final QuestionnaireLogic questionnaireLogic = QuestionnaireLogic();
  final UniversalWidgets universals = UniversalWidgets();
  final QuestionnaireLogicReader questionnaireLogicReader =
      QuestionnaireLogicReader();
  List<TextEditingController> controllers = [];
  final ValueNotifier<bool> _isDeleting = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<List<OrderedItem>> dataFuture =
        questionnaireLogic.fetchQuestionnaireData(ref);
    final projectId = ref.watch(currentProjectID);
    final projectName = ref.watch(currentProjectName);

    final FirebaseAuth auth = FirebaseAuth.instance;
    final User? user = auth.currentUser;
    final uid = user?.uid;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // const SizedBox(height: 16),

                  // Project name header
                  // Padding(
                  //   padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
                  //   child: Text(
                  //     projectName,
                  //     style: TextStyle(
                  //       fontSize: 18,
                  //       fontWeight: FontWeight.w600,
                  //       color: globals.bookBranchGreen,
                  //     ),
                  //   ),
                  // ),
                  // Subscription status card
                  _buildCard(
                    context,
                    Consumer(builder: (context, ref, child) {
                      final isSubscribed =
                          ref.watch(isSubscribedProvider(projectId));
                      return isSubscribed.when(
                        data: (isSubscribed) => isSubscribed
                            ? Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: const Color.fromARGB(25, 44, 148, 44),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        globals.bookBranchGreen.withAlpha(75),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: globals.bookBranchGreen
                                            .withAlpha(25),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.check_circle_outline,
                                        color: globals.bookBranchGreen,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Text(
                                        'You are already subscribed to this project. There is no need to resubmit your application.',
                                        style: TextStyle(
                                          fontSize: 15,
                                          color: globals.bookBranchGreen,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox.shrink(),
                        loading: () => const Center(
                          child: CircularProgressIndicator(
                            color: Color.fromARGB(255, 44, 148, 44),
                          ),
                        ),
                        error: (e, _) => Text(
                          'Error fetching subscription status: $e',
                          style: const TextStyle(color: Colors.red),
                        ),
                      );
                    }),
                  ),
                  const SizedBox(height: 16),

                  // Application questions section header
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
                    child: Text(
                      'Application Questions',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                  // Application questions card
                  _buildCard(
                    context,
                    FutureBuilder<List<OrderedItem>>(
                      future: dataFuture,
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return Container(
                            padding: const EdgeInsets.all(24.0),
                            alignment: Alignment.center,
                            child: const CircularProgressIndicator(
                              color: Color.fromARGB(255, 44, 148, 44),
                            ),
                          );
                        } else if (snapshot.hasError) {
                          return Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                Icon(Icons.error_outline,
                                    color: Colors.red[700], size: 48),
                                const SizedBox(height: 16),
                                Text(
                                  'Error loading questions',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red[700],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '${snapshot.error}',
                                  style: const TextStyle(color: Colors.red),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          );
                        } else if (snapshot.hasData &&
                            snapshot.data!.isNotEmpty) {
                          controllers = List.generate(snapshot.data!.length,
                              (_) => TextEditingController());

                          List<Widget> textFields = snapshot.data!.map((item) {
                            int index = snapshot.data!.indexOf(item);
                            return Container(
                              margin: const EdgeInsets.only(bottom: 16.0),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.grey.shade300,
                                  width: 1,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Question ${index + 1}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      item.question,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      controller: controllers[index],
                                      minLines: 3,
                                      maxLines: 5,
                                      decoration: InputDecoration(
                                        hintText: 'Type your answer here...',
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: Colors.grey.shade300,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: globals.bookBranchGreen,
                                            width: 2,
                                          ),
                                        ),
                                        contentPadding:
                                            const EdgeInsets.all(16),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }).toList();

                          // Submit button
                          textFields.add(
                            Container(
                              margin: const EdgeInsets.only(bottom: 16.0),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16.0),
                                child: ElevatedButton(
                                  onPressed: () async {
                                    // Check if user is logged in
                                    if (uid == null) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'You must be logged in to submit an application'),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                      return;
                                    }

                                    // Retrieve user data and check subscription status
                                    UserMetadataFetch? userData =
                                        await UserMetadataFetch
                                            .fetchUserMetaData(uid);
                                    final isSubscribedValue = ref
                                        .read(isSubscribedProvider(projectId))
                                        .maybeWhen(
                                          data: (isSubscribed) => isSubscribed,
                                          orElse: () => false,
                                        );

                                    if (isSubscribedValue) {
                                      // If already subscribed, just close the page
                                      if (context.mounted) {
                                        Navigator.popUntil(
                                            context,
                                            ModalRoute.withName(
                                                MarketPlaceScreenReader
                                                    .routeName));
                                      }
                                    } else {
                                      if (context.mounted) {
                                        universals.showCustomPopupWithCheckbox(
                                            context: context,
                                            titleText: "Submit Application",
                                            contentText:
                                                "Are you ready to submit your application?",
                                            checkboxText: "I'm Ready",
                                            cancelButtonText: "Cancel",
                                            actionButtonText: "Submit",
                                            onActionPressed: () async {
                                              _isDeleting.value =
                                                  true; // Start deleting

                                              List<Map<String, dynamic>>
                                                  itemsToSend =
                                                  snapshot.data!.map((item) {
                                                item.answer = controllers[
                                                        snapshot.data!
                                                            .indexOf(item)]
                                                    .text;
                                                return item.toMap();
                                              }).toList();

                                              FirebaseFunctions functions =
                                                  FirebaseFunctions.instance;
                                              HttpsCallable callable =
                                                  functions.httpsCallable(
                                                      'submitApplicationQuestions');

                                              try {
                                                // Call the function with all necessary data
                                                await callable.call({
                                                  'items': itemsToSend,
                                                  'projectID': projectId,
                                                  'displayName':
                                                      userData!.displayName
                                                });

                                                _isDeleting.value = false;

                                                if (context.mounted) {
                                                  Navigator.pop(context);

                                                  // Show success message
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                      content: const Text(
                                                          'Application submitted successfully!'),
                                                      backgroundColor: globals
                                                          .bookBranchGreen,
                                                      behavior: SnackBarBehavior
                                                          .floating,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                    ),
                                                  );

                                                  // Navigate back to marketplace
                                                  Future.delayed(
                                                      const Duration(
                                                          seconds: 1), () {
                                                    if (context.mounted) {
                                                      Navigator.popUntil(
                                                          context,
                                                          ModalRoute.withName(
                                                              MarketPlaceScreenReader
                                                                  .routeName));
                                                    }
                                                  });
                                                }
                                              } catch (e) {
                                                _isDeleting.value = false;
                                                // Error handling with proper logging
                                                debugPrint(
                                                    "Error calling function: $e");

                                                if (context.mounted) {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                      content:
                                                          Text('Error: $e'),
                                                      backgroundColor:
                                                          Colors.red,
                                                      behavior: SnackBarBehavior
                                                          .floating,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                    ),
                                                  );
                                                }
                                              }
                                            });
                                      }
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: globals.bookBranchGreen,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: const Text(
                                    'Submit Application',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisSize: MainAxisSize.min,
                            children: textFields,
                          );
                        } else {
                          return Padding(
                            padding: const EdgeInsets.all(24.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.help_outline,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No questions available',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.grey[700],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'The project owner has not set up any application questions yet.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
              // Loading overlay
              ValueListenableBuilder<bool>(
                valueListenable: _isDeleting,
                builder: (context, isDeleting, _) {
                  if (isDeleting) {
                    return Positioned.fill(
                      child: Container(
                        color: Colors.black.withAlpha(128),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(25),
                                  blurRadius: 10,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(
                                  color: globals.bookBranchGreen,
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'Submitting application...',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
