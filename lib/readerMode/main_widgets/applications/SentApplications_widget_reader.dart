import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/readerMode/main_logic/questionnaire_logic_reader.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';

class SentApplicationsWidgetReader extends ConsumerWidget {
  SentApplicationsWidgetReader({super.key});

  final UniversalWidgets universalWidgets = UniversalWidgets();
  final QuestionnaireLogicReader questionnaireLogicReader =
      QuestionnaireLogicReader();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final User? user = FirebaseAuth.instance.currentUser;
    final String uid = user?.uid ?? '';

    return Scaffold(
      body: FutureBuilder<List<String>>(
        future: questionnaireLogicReader.fetchSentApplications(uid),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Container(
                  width: double.infinity,
                  constraints: const BoxConstraints(maxWidth: 400),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: globals.bookBranchGreen.withAlpha(50),
                      width: 1,
                    ),
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'No Pending Applications',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'You do not have any pending applications yet.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Visit the Marketplace to discover projects and submit applications.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),

                      // Action button
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context)
                              .pushNamed(MarketPlaceScreenReader.routeName);
                        },
                        icon: const Icon(Icons.shopping_bag_outlined),
                        label: const Text('Browse Marketplace'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: globals.bookBranchGreen,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          } else {
            List<String> projects = snapshot.data!;
            return ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: projects.length,
              itemBuilder: (context, index) {
                String projectName = projects[index];
                return SizedBox(
                  height: 100,
                  child: universalWidgets.buildColorfulCard(
                    context,
                    projectName,
                    () {
                      // Define what happens when the card is tapped
                      // Handle tap on $projectName
                    },
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}
