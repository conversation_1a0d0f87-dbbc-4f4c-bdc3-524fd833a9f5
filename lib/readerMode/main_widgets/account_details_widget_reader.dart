import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:web_app/readerMode/main_logic/account_details_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';

class AccountDetailsWidgetReader extends ConsumerWidget {
  final UniversalWidgets universal = UniversalWidgets();
  final FetchDatabase fetch = FetchDatabase();
  final WriteDatabase writeMetadata = WriteDatabase();
  AuthenticationLogic authObject = AuthenticationLogic();
  AccountDetailsLogic accountDetailsLogic = AccountDetailsLogic();
  final TextEditingController _displayNameController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder(
      future:
          Future.wait([fetch.fetchDisplayName(), fetch.fetchProjectCount()]),
      builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator(); // Show loading indicator
        } else if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        } else if (snapshot.hasData) {
          var username = snapshot.data![0]; // Data from getUsername
          var projectCount = snapshot.data![1]; // Data from getOtherData

          return Scaffold(
            backgroundColor: Colors.grey[100],
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                      height: MediaQuery.of(context).size.height *
                          0.04), // Adjusted space
                  universal.buildProfilePicture(username, Colors.red),
                  const SizedBox(height: 24.0),
                  universal.buildEditableMinimalistField(
                      'Display Name', _displayNameController, username, 8.0),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: universal.buildButtonFlat(
                      'Change Display Name',
                      true,
                      () => writeMetadata.updateDisplayName(
                          _displayNameController.text, ref), // Corrected
                      Colors.red,
                      Colors.white,
                    ),
                  ),
                  universal.buildMinimalistField(
                      'Number of Projects', projectCount.toString()),
                  universal.buildMinimalistField(
                      'Account Plan', ref.watch(planTypeProvider)),
                ],
              ),
            ),
          );
        } else {
          return const Text('No data available');
        }
      },
    );
  }

  void _saveChanges() {
    print('Display name saved');
  }
}
