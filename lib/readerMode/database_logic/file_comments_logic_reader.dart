import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/user_metadata_fetch.dart';
import 'package:web_app/readerMode/database_logic/file_comments_state_management_reader.dart';

import 'package:tuple/tuple.dart';

class FileCommentsLogicReader {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<void> postFileComment(String comment, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    UserMetadataFetch? userData =
        await UserMetadataFetch.fetchUserMetaData(uid!);
    final displayName = userData!.displayName;

    try {
      String fileUrl = ref.watch(chosenFileUrl);
      DocumentReference docRef = db.collection("fileComments").doc();

      await docRef.set({
        'comment': comment,
        'fileUrl': fileUrl,
        'userID': auth.currentUser!.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'likes': 0,
        'displayName': displayName,
        'commentID': docRef.id,
        'projectID': ref.watch(currentProjectID)
      });

      ref.invalidate(topLevelCommentsProvider);
      ref.refresh(topLevelCommentsProvider(ref.watch(chosenFileUrl)));

      print("Comment posted successfully.");
    } catch (e) {
      print("Error posting comment: $e");
    }
  }

  Future<void> postNestedFileComment(
      String comment, String parentCommentID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;

    UserMetadataFetch? userData =
        await UserMetadataFetch.fetchUserMetaData(uid!);
    final displayName = userData!.displayName;

    try {
      String fileUrl = ref.watch(chosenFileUrl);
      DocumentReference docRef = db
          .collection("fileComments")
          .doc(parentCommentID)
          .collection("nestedComments")
          .doc();

      await docRef.set({
        'comment': comment,
        'fileUrl': fileUrl,
        'userID': auth.currentUser!.uid,
        'timestamp': FieldValue.serverTimestamp(),
        'likes': 0,
        'displayName': displayName,
        'commentID': docRef.id,
        'parentCommentID': parentCommentID,
        'projectID': ref.watch(currentProjectID)
      });

      ref.refresh(fileCommentsRepliesProvider(
          Tuple2(parentCommentID, ref.watch(chosenFileUrl))));

      print("Comment posted successfully.");
    } catch (e) {
      print("Error posting comment: $e");
    }
  }

  Future<void> likeComment(
      String commentID, String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    ref.watch(fileCommentsRepliesProvider(
        Tuple2(commentID, ref.watch(chosenFileUrl))));
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef = db.collection("likedFileComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedFileComments");

      // Reference to the comment document
      DocumentReference commentDocRef =
          db.collection("fileComments").doc(commentID);

      // Check if the user has already liked the comment
      QuerySnapshot likedDocSnapshot = await likedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: uid)
          .get();
      if (likedDocSnapshot.docs.isNotEmpty) {
        print("User has already liked this comment.");
        return;
      }

      // If the user has disliked the comment, neutralize the dislike first
      QuerySnapshot dislikedDocSnapshot = await dislikedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (dislikedDocSnapshot.docs.isNotEmpty) {
        await dislikedCommentsRef
            .doc(dislikedDocSnapshot.docs.first.id)
            .delete();
        // Neutralizing the dislike by incrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(1),
        });

        // Refresh the provider to update the UI
        ref.refresh(fileCommentsLikesProvider(commentID));
        print("Comment neutralized from dislike.");
        return;
      }

      // Now, like the comment
      await likedCommentsRef.add({
        // Change from doc().set() to add() to create a new document
        'commentID': commentID,
        'fileUrl': ref.watch(
            chosenFileUrl), // This should be replaced with the actual file URL
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(1),
      });

      // Refresh the provider to update the UI
      ref.refresh(fileCommentsLikesProvider(commentID));
      print("Comment liked successfully.");
    } catch (e) {
      print("Error liking comment: $e");
    }
  }

  Future<void> dislikeComment(
      String commentID, String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    ref.watch(fileCommentsRepliesProvider(
        Tuple2(commentID, ref.watch(chosenFileUrl))));
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef = db.collection("likedFileComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedFileComments");

      // Reference to the comment document
      DocumentReference commentDocRef =
          db.collection("fileComments").doc(commentID);

      // Check if the user has already disliked the comment
      QuerySnapshot dislikedDocSnapshot = await dislikedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: uid)
          .get();
      if (dislikedDocSnapshot.docs.isNotEmpty) {
        print("User has already disliked this comment.");
        return;
      }

      // If the user has liked the comment, neutralize the like first
      QuerySnapshot likedDocSnapshot = await likedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (likedDocSnapshot.docs.isNotEmpty) {
        await likedCommentsRef.doc(likedDocSnapshot.docs.first.id).delete();
        // Neutralizing the like by decrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(-1),
        });

        // Refresh the provider to update the UI
        ref.refresh(fileCommentsLikesProvider(commentID));
        print("Like neutralized for the comment.");
        return;
      }

      // Now, dislike the comment
      await dislikedCommentsRef.add({
        // Change from doc().set() to add() to create a new document
        'commentID': commentID,
        'fileUrl': ref.watch(
            chosenFileUrl), // This should be replaced with the actual file URL
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(-1),
      });

      // Refresh the provider to update the UI
      ref.refresh(fileCommentsLikesProvider(commentID));
      print("Comment disliked successfully.");
    } catch (e) {
      print("Error disliking comment: $e");
    }
  }

  Future<void> likeNestedComment(String parentCommentID, String commentID,
      String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef =
          db.collection("likedNestedFileComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedNestedFileComments");

      // Reference to the nested comment document
      DocumentReference commentDocRef = db
          .collection("fileComments")
          .doc(parentCommentID)
          .collection("nestedComments")
          .doc(commentID);

      // Check if the user has already liked the comment
      QuerySnapshot likedDocSnapshot = await likedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: uid)
          .get();

      if (likedDocSnapshot.docs.isNotEmpty) {
        print("User has already liked this comment.");
        return;
      }

      // If the user has disliked the comment, neutralize the dislike first
      QuerySnapshot dislikedDocSnapshot = await dislikedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (dislikedDocSnapshot.docs.isNotEmpty) {
        await dislikedCommentsRef
            .doc(dislikedDocSnapshot.docs.first.id)
            .delete();
        // Neutralizing the dislike by incrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(1),
        });

        ref.invalidate(fileCommentsNestedLikesProvider(
            Tuple2(parentCommentID, commentID)));
        ref.refresh(fileCommentsNestedLikesProvider(
            Tuple2(parentCommentID, commentID)));

        print("Like neutralized for the comment.");
        return;
      }

      // Now, like the comment
      await likedCommentsRef.add({
        'commentID': commentID,
        'parentCommentID': parentCommentID,
        'fileUrl':
            ref.watch(chosenFileUrl), // Replace this with the actual file URL
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(1),
      });

      ref.invalidate(
          fileCommentsNestedLikesProvider(Tuple2(parentCommentID, commentID)));
      ref.refresh(
          fileCommentsNestedLikesProvider(Tuple2(parentCommentID, commentID)));

      print("Comment liked successfully.");
    } catch (e) {
      print("Error liking comment: $e");
    }
  }

  Future<void> dislikeNestedComment(String parentCommentID, String commentID,
      String userID, WidgetRef ref) async {
    final User? user = auth.currentUser;
    final uid = user?.uid;
    try {
      // References to the liked and disliked comments collections
      CollectionReference likedCommentsRef =
          db.collection("likedNestedFileComments");
      CollectionReference dislikedCommentsRef =
          db.collection("dislikedNestedFileComments");

      // Reference to the nested comment document
      DocumentReference commentDocRef = db
          .collection("fileComments")
          .doc(parentCommentID)
          .collection("nestedComments")
          .doc(commentID);

      // Check if the user has already disliked the comment
      QuerySnapshot dislikedDocSnapshot = await dislikedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: uid)
          .get();
      if (dislikedDocSnapshot.docs.isNotEmpty) {
        print("User has already disliked this comment.");
        return;
      }

      // If the user has liked the comment, neutralize the like first
      QuerySnapshot likedDocSnapshot = await likedCommentsRef
          .where('commentID', isEqualTo: commentID)
          .where('userID', isEqualTo: userID)
          .get();

      if (likedDocSnapshot.docs.isNotEmpty) {
        await likedCommentsRef.doc(likedDocSnapshot.docs.first.id).delete();
        // Neutralizing the like by decrementing likes
        await commentDocRef.update({
          'likes': FieldValue.increment(-1),
        });

        ref.invalidate(fileCommentsNestedLikesProvider(
            Tuple2(parentCommentID, commentID)));
        ref.refresh(fileCommentsNestedLikesProvider(
            Tuple2(parentCommentID, commentID)));

        print("Like neutralized for the comment.");
        return;
      }

      // Now, dislike the comment
      await dislikedCommentsRef.add({
        // Change from doc().set() to add() to create a new document
        'commentID': commentID,
        'parentCommentID': parentCommentID,
        'fileUrl':
            ref.watch(chosenFileUrl), // Replace this with the actual file URL
        'projectID': ref.watch(currentProjectID),
        'userID': uid
      });

      await commentDocRef.update({
        'likes': FieldValue.increment(-1),
      });

      ref.invalidate(
          fileCommentsNestedLikesProvider(Tuple2(parentCommentID, commentID)));
      ref.refresh(
          fileCommentsNestedLikesProvider(Tuple2(parentCommentID, commentID)));

      print("Comment disliked successfully.");
    } catch (e) {
      print("Error disliking comment: $e");
    }
  }
}
