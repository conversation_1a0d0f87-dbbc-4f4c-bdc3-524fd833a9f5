import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tuple/tuple.dart';
import 'package:web_app/readerMode/database_logic/database_logic_fetch/fetch_file_comments_reader.dart';

final topLevelCommentsProvider =
    FutureProvider.family<int, String>((ref, fileUrl) async {
  return FetchFileCommentsReader().fetchNumComments(fileUrl);
});

final fileCommentsRepliesProvider =
    FutureProvider.family<int, Tuple2<String, String>>((ref, tuple) async {
  return FetchFileCommentsReader()
      .fetchNumberOfReplies(tuple.item1, tuple.item2);
});

final fileCommentsLikesProvider =
    FutureProvider.family<int, String>((ref, commentID) async {
  return FetchFileCommentsReader().fetchNumberOfLikes(commentID);
});

final fileCommentsNestedLikesProvider =
    FutureProvider.family<int, Tuple2<String, String>>((ref, tuple) async {
  return FetchFileCommentsReader()
      .fetchNumberOfNestedLikes(tuple.item1, tuple.item2);
});
