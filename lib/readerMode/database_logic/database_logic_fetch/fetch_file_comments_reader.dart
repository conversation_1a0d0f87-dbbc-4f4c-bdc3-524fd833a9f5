import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';

class FetchFileCommentsReader {
  Future<List<List<dynamic>>> fetchFileCommentData(WidgetRef ref) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    List<String> commentsList = [];
    List<String> displayNameList = [];
    List<int> likesList = [];
    List<Timestamp> timeStampList = [];
    List<List<dynamic>> everything = [];
    List<String> commentIDList = [];
    try {
      final querySnapshot = await db
          .collection("fileComments")
          .where('fileUrl', isEqualTo: ref.watch(chosenFileUrl))
          .orderBy('timestamp', descending: true)
          .get();
      if (querySnapshot.docs.isNotEmpty) {
        for (var doc in querySnapshot.docs) {
          var commentData = doc['comment'];
          var displayName = doc['displayName'];
          var likes = doc['likes'];
          var timeStamp = doc['timestamp'];
          var commentID = doc['commentID'];
          if (commentData is String) {
            commentsList.add(commentData);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (displayName is String) {
            displayNameList.add(displayName);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (likes is int) {
            likesList.add(likes);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (timeStamp is Timestamp) {
            timeStampList.add(timeStamp);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
          if (commentID is String) {
            commentIDList.add(commentID);
          } else {
            print(
                "Unexpected data type for comment: ${commentData.runtimeType}");
          }
        }
        everything.add(displayNameList);
        everything.add(timeStampList);
        everything.add(commentsList);
        everything.add(likesList);
        everything.add(commentIDList);
        print("Comments fetched successfully.");
        print(everything);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error fetching comments: $e");
    }
    return everything;
  }

  Future<List<dynamic>> fetchNestedComments(
      String parentCommentID, String fileUrl, WidgetRef ref,
      {int currentDepth = 0}) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    List<dynamic> comments = [];
    //currentDepth = 0;

    try {
      var querySnapshot = await db
          .collection("fileComments")
          .doc(parentCommentID)
          .collection("nestedComments")
          .where('fileUrl', isEqualTo: fileUrl)
          .orderBy('likes', descending: true)
          .get();

      for (var doc in querySnapshot.docs) {
        var comment = {
          'displayName': doc['displayName'],
          'likes': doc['likes'],
          'timestamp': (doc['timestamp'] as Timestamp).toDate(),
          'comment': doc['comment'],
          'commentID': doc.id,
          'parentCommentID': parentCommentID,
          'depth': currentDepth, // Add current depth to the comment info
          'nestedComments': [],
        };

        // Recursively fetch nested comments and increment depth
        var nestedComments = await fetchNestedComments(doc.id, fileUrl, ref,
            currentDepth: currentDepth + 1);
        if (nestedComments.isNotEmpty) {
          comment['nestedComments'] = nestedComments;
        }

        comments.add(comment);
      }
    } catch (e) {
      print("Error fetching nested comments: $e");
    }

    return comments;
  }

  Future<int> fetchNumComments(String fileUrl) async {
    try {
      var querySnapshot = await FirebaseFirestore.instance
          .collection("fileComments")
          .where('fileUrl', isEqualTo: fileUrl)
          .get();

      int numComments = querySnapshot.docs.length;
      return numComments;
    } catch (e) {
      print("Error fetching number of comments: $e");
      return 0;
    }
  }

  // Future<List<dynamic>> fetchNestedComments(
  //     String parentCommentID, String fileUrl, WidgetRef ref,
  //     {int currentDepth = 0}) async {
  //   FirebaseFirestore db = FirebaseFirestore.instance;
  //   List<dynamic> comments = [];
  //   //currentDepth = 0;

  //   try {
  //     var querySnapshot = await db
  //         .collection("fileComments")
  //         .doc(parentCommentID)
  //         .collection("nestedComments")
  //         .where('fileUrl', isEqualTo: fileUrl)
  //         .orderBy('likes', descending: true)
  //         .get();

  //     for (var doc in querySnapshot.docs) {
  //       var comment = {
  //         'displayName': doc['displayName'],
  //         'likes': doc['likes'],
  //         'timestamp': (doc['timestamp'] as Timestamp).toDate(),
  //         'comment': doc['comment'],
  //         'commentID': doc.id,
  //         'parentCommentID': parentCommentID,
  //         'depth': currentDepth, // Add current depth to the comment info
  //         'nestedComments': [],
  //       };

  //       // Recursively fetch nested comments and increment depth
  //       var nestedComments = await fetchNestedComments(doc.id, fileUrl, ref,
  //           currentDepth: currentDepth + 1);
  //       if (nestedComments.isNotEmpty) {
  //         comment['nestedComments'] = nestedComments;
  //       }

  //       comments.add(comment);
  //     }
  //   } catch (e) {
  //     print("Error fetching nested comments: $e");
  //   }

  //   return comments;
  // }

  Future<int> fetchNumberOfReplies(
      String parentCommentID, String fileUrl) async {
    try {
      var querySnapshot = await FirebaseFirestore.instance
          .collection("fileComments")
          .doc(parentCommentID)
          .collection("nestedComments")
          .where('fileUrl', isEqualTo: fileUrl)
          .get();

      int numberOfReplies = querySnapshot.docs.length;
      return numberOfReplies;
    } catch (e) {
      print("Error fetching number of replies: $e");
      return 0;
    }
  }

  Future<int> fetchNumberOfLikes(String commentID) async {
    try {
      var docSnapshot = await FirebaseFirestore.instance
          .collection("fileComments")
          .doc(commentID)
          .get();

      int numberOfLikes = docSnapshot['likes'];
      return numberOfLikes;
    } catch (e) {
      print("Error fetching number of likes: $e");
      return 0;
    }
  }

  Future<int> fetchNumberOfNestedLikes(
      String parentCommentID, String commentID) async {
    try {
      var docSnapshot = await FirebaseFirestore.instance
          .collection("fileComments")
          .doc(parentCommentID)
          .collection('nestedComments')
          .doc(commentID)
          .get();

      int numberOfLikes = docSnapshot['likes'];
      return numberOfLikes;
    } catch (e) {
      print("Error fetching number of likes: $e");
      return 0;
    }
  }
}
