import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:web_app/dartMain/dartMain_screens/authentication_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/dartMain/dartMain_screens/main_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/project_details_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/projects_screen.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_logic/write_database_subsections.dart';
import 'package:web_app/database/database_screens/epublishing_files_screen.dart';
import 'package:web_app/readerMode/database_screens/file_comments_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/main_screen_reader.dart';
import 'package:web_app/dartMain/dartMain_screens/account_details_screen.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/readerMode/main_screens/marketplace_project_detail_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/subscriptions_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/account_details_reader_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/help_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/legal_screen.dart';
import 'package:web_app/readerMode/main_logic/marketplace_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/dartMain_logic_fetch/fetch_project_data.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/dartMain/dartMain_screens/forgot_password_screen.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:web_app/dartMain/dartMain_logic/fetch_main.dart';
import 'package:web_app/dartMain/dartMain_logic/on_login.dart';
import 'package:web_app/readerMode/main_screens/SentApplications_screen_reader.dart';
import 'package:web_app/dartMain/dartMain_screens/projects_with_applications_list_screen.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_provider_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/applications/questionnaire_logic.dart';
import 'package:web_app/readerMode/main_logic/main_logic_fetch/subscriptions_logic_fetch.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/readerMode/main_logic/account_details_logic.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'dart:io' show Platform;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  print("Widgets binding initialized");

  FirebaseOptions options;

  // Define Firebase options for each platform
  if (Platform.isIOS) {
    options = const FirebaseOptions(
      apiKey: "AIzaSyAu5WZ-pGt8aWdENUrBHhBBmMIQfEww9xM",
      appId: "1:************:ios:0359e0762c94070d92065d",
      messagingSenderId: "************",
      storageBucket: "bookbox-5153e.appspot.com",
      projectId: "bookbox-5153e",
    );
  } else if (Platform.isAndroid) {
    options = const FirebaseOptions(
      apiKey: "AIzaSyCPJ_koH2uMDDRs2ub5F65rX_0tDPMDw6s",
      appId: "1:************:android:60084f44fae3822b92065d",
      messagingSenderId: "************",
      storageBucket: "bookbox-5153e.appspot.com",
      projectId: "bookbox-5153e",
    );
  } else {
    throw UnsupportedError("This platform is not supported");
  }

  // Initialize Firebase differently based on the platform
  if (Firebase.apps.isEmpty) {
    if (Platform.isIOS) {
      await Firebase.initializeApp(name: 'BookBranch', options: options);
    } else {
      await Firebase.initializeApp(options: options);
    }
    print("Firebase initialized");
  } else {
    print("Firebase already initialized");
  }

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  runApp(const ProviderScope(child: MyApp()));
}

// the following is used for push notifications ----------------------------

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("Handling a background message: ${message.messageId}");
}

void requestPermissions() async {
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    print('User granted permission');
  } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
    print('User granted provisional permission');
  } else {
    print('User declined or has not accepted permission');
  }
}

void saveDeviceToken([String? token]) async {
  token ??= await FirebaseMessaging.instance.getToken();
  if (token != null) {
    // Save the token to Firestore
    String userId = FirebaseAuth.instance.currentUser!.uid;
    await FirebaseFirestore.instance.collection('users').doc(userId).update({
      'fcmToken': token,
    });
    print('FCM token saved to Firestore: $token');
  }
}

// ---------------------------------------------------------------------------

class MyApp extends ConsumerStatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  AccountDetailsLogic accountDetailsLogic = AccountDetailsLogic();

  @override
  void initState() {
    super.initState();
    initRevenueCatPlatformState();

    _initializeAppAsync();

    // Request permissions for iOS
    requestPermissions();

    // Get FCM token and save to Firestore
    saveDeviceToken();

    // Handle token refresh
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
      saveDeviceToken(newToken);
    });
  }

  Future<void> _initializeAppAsync() async {
    try {
      CustomerInfo customerInfo = await Purchases.getCustomerInfo();

      if (customerInfo.entitlements.all["BookBranch+"]?.isActive != true) {
        // You may need to adjust your logic based on how you access `accountDetailsLogic` and `ref`
        accountDetailsLogic.downgradePlanType(ref);
      }
    } catch (e) {
      // Handle errors if needed
      print("Error fetching customer info: $e");
    }
  }

  // revenuecat
  Future<void> initRevenueCatPlatformState() async {
    PurchasesConfiguration configuration;
    if (Platform.isAndroid) {
      configuration =
          PurchasesConfiguration("goog_OsVXTaYzLcgxoussORNOXlZaqHK");
    } else if (Platform.isIOS) {
      configuration =
          PurchasesConfiguration("appl_IbErrhcrjlwQGMPOzsRzKKfwJHk");
    } else {
      configuration =
          PurchasesConfiguration("appl_IbErrhcrjlwQGMPOzsRzKKfwJHk");
    }
    await Purchases.configure(configuration);
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BookBranch Login',
      navigatorKey: navigatorKey,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      routes: {
        MainScreen.routeName: (context) => const MainScreen(),
        ProjectDetails.routeName: (context) => ProjectDetails(),
        ProjectsScreen.routeName: (context) => const ProjectsScreen(),
        //DatabaseMain.routeName: (context) => DatabaseMain(),
        MainScreenReader.routeName: (context) => MainScreenReader(),
        AccountDetailsScreen.routeName: (context) => AccountDetailsScreen(),
        AuthenticationScreen.routeName: ((context) => AuthenticationScreen()),
        MarketPlaceScreenReader.routeName: (context) =>
            MarketPlaceScreenReader(),
        MarketPlaceProjectDetailScreenReader.routeName: (context) =>
            MarketPlaceProjectDetailScreenReader(),
        SubscriptionsScreenReader.routeName: (context) =>
            SubscriptionsScreenReader(),
        AccountDetailsReaderScreen.routeName: (context) =>
            AccountDetailsReaderScreen(),
        // More submenu
        HelpScreen.routeName: (context) => HelpScreen(),
        LegalScreen.routeName: (context) => LegalScreen(),
        FileCommentsScreenReader.routeName: (context) =>
            FileCommentsScreenReader(),
        ForgotPasswordScreen.routeName: (context) => ForgotPasswordScreen(),
        SentApplicationsScreenReader.routeName: (context) =>
            SentApplicationsScreenReader(),
        ProjectsWithApplicationsListScreen.routeName: (context) =>
            ProjectsWithApplicationsListScreen(),
        EPublishingFilesScreen.routeName: (context) => EPublishingFilesScreen(),
      },
      home: AuthenticationScreen(),
    );
  }
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Riverpod data
FetchDatabase fetch = FetchDatabase();
final MarketPlaceLogic logic = MarketPlaceLogic();
FetchProjectData fetchProjectData = FetchProjectData();
FetchMain fetchMain = FetchMain();
OnLogin onLogin = OnLogin();
QuestionnaireLogic questionnaireLogic = QuestionnaireLogic();
QuestionnaireProviderLogic questionnaire = QuestionnaireProviderLogic();
SubscriptionsLogicFetch subscriptionsLogicFetch = SubscriptionsLogicFetch();

final currentProjectID = StateProvider<String>((ref) {
  return "";
});

final currentProjectVisibleIDProvider = StateProvider<String>((ref) {
  return "";
});

final currentProjectName = StateProvider<String>((ref) {
  return "";
});

final currentUserFirstLetterProvider = StateProvider<String>((ref) {
  return "";
});

// Database, Community, or E-Publishing
final currentProjectSubSection = StateProvider<String>((ref) {
  return "";
});

// default value is 'chapters' defined in write_firestore.dart on project create
final currentProjectLayout = StateProvider<String>((ref) {
  return "";
});

final currentChapter = StateProvider<String>((ref) {
  return "";
});

final currentEPublishingChapter = StateProvider<String>((ref) {
  return "";
});

final currentSubsection = StateProvider<String>((ref) {
  return "";
});

final currentEPublishingSubsection = StateProvider<String>((ref) {
  return "";
});

final titlesProvider = FutureProvider<List<String>>((ref) async {
  final fetchDatabase = FetchDatabase();
  final projectID = ref.watch(currentProjectID);
  // Assuming `currentProjectIDProvider` is a provider that exposes the current project ID
  return fetchDatabase.fetchDocNamesChaptersWidget(projectID);
});

final titlesProviderEPublishing = FutureProvider<List<String>>((ref) async {
  final fetchDatabase = FetchDatabase();
  final projectID = ref.watch(currentProjectID);
  // Assuming `currentProjectIDProvider` is a provider that exposes the current project ID
  return fetchDatabase.fetchDocNamesEPublishingChaptersWidget(projectID);
});

final chosenFileUrl = StateProvider<String>((ref) {
  return "";
});

final chosenFileType = StateProvider<String>((ref) {
  return "";
});

// --- state providers --------------------------------------------
// subsections state tracker
final subsectionListProvider =
    StateNotifierProvider<WriteDatabaseSubsections, List<String>>((ref) {
  return WriteDatabaseSubsections();
});

// number of files state tracker
final fileListProvider = StateProvider<List<FileMetadata>>((ref) => []);

final fileMetadataProvider = FutureProvider<List<FileMetadata>>((ref) async {
  // Implement the logic to fetch file metadata based on the path
  return FileMetadata.fetchFileMetadata(ref.watch(currentChapter),
      ref.watch(currentSubsection), ref.watch(currentProjectID));
});

final fileMetadataProviderEPublishing =
    FutureProvider<List<FileMetadata>>((ref) async {
  // Implement the logic to fetch file metadata based on the path
  return FileMetadata.fetchFileMetadataEPublishing(
      ref.watch(currentEPublishingChapter),
      ref.watch(currentEPublishingSubsection),
      ref.watch(currentProjectID));
});

final fileMetadataProviderChapterLevel =
    FutureProvider<List<FileMetadata>>((ref) async {
  // Implement the logic to fetch file metadata based on the path
  return FileMetadata.fetchFileMetadataChapterLevel(ref.watch(currentChapter));
});

final fileMetadataProviderEPublishingChapterLevel =
    FutureProvider<List<FileMetadata>>((ref) async {
  // Implement the logic to fetch file metadata based on the path
  return FileMetadata.fetchFileMetadataEPublishingChapterLevel(
      ref.watch(currentEPublishingChapter));
});

//final modeProvider = StateProvider<String>((ref) => 'Creator Mode');

final modeProvider = StateProvider<String>((ref) => 'Reader Mode');

final projectCountProvider = FutureProvider<Future<int>>((ref) async {
  return fetch.fetchProjectCount();
});

final projectVisibilityFutureProvider =
    FutureProvider.family<String, String>((ref, projectID) async {
  // Assuming getProjectVisibility now just fetches and returns the visibility
  return fetch.fetchProjectVisibility(projectID);
});

final projectDescriptionProvider = StateProvider<String>((ref) {
  return "";
});

// final displayNameProvider = FutureProvider<String>((ref) {
//   return fetch.fetchUsername();
// });

final displayNameProvider = StateProvider<String>((ref) {
  return "";
});

final planTypeProvider = StateProvider<String>((ref) {
  return "";
});

final currentProjectThumbnailName = StateProvider<String>((ref) {
  return "";
});

final currentProjectThumbnailUrlProvider = StateProvider<String>((ref) {
  return "";
});

final isLoadingProvider = StateProvider<bool>((ref) => false);

final fetchProjectTagsProvider = StreamProvider<List<String>>((ref) {
  String projectId = ref.watch(currentProjectID);
  FirebaseFirestore db = FirebaseFirestore.instance;

  return db.collection('projects').doc(projectId).snapshots().map((snapshot) {
    if (snapshot.exists && snapshot.data()!['tags'] != null) {
      List<dynamic> tags = snapshot.data()!['tags'];
      return tags.cast<String>();
    }
    return <String>[];
  });
});

final fetchFileTagsProvider = StreamProvider<List<String>>((ref) async* {
  FirebaseFirestore db = FirebaseFirestore.instance;
  String fileUrl =
      ref.watch(chosenFileUrl); // This should be correctly providing the URL

  if (fileUrl.isEmpty || !fileUrl.contains('http')) {
    print("Invalid or empty URL: $fileUrl");
    yield <String>[];
    return;
  }

  try {
    var querySnapshot = await db
        .collection('data')
        .where('File URL', isEqualTo: fileUrl)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      var document = querySnapshot.docs.first;
      var documentId = document.id;

      yield* db.collection('data').doc(documentId).snapshots().map((snapshot) {
        if (snapshot.exists &&
            snapshot.data() != null &&
            snapshot.data()!.containsKey('tags')) {
          List<dynamic> tags = snapshot.data()!['tags'];
          return tags.cast<String>();
        }
        return <String>[];
      });
    } else {
      print("No document found for URL: $fileUrl");
      yield <String>[];
    }
  } catch (e) {
    print("Error fetching tags for URL $fileUrl: $e");
    yield <String>[]; // Provide an empty list on error
  }
});

final commentIdProvider = StateProvider<String>((ref) {
  return "";
});

final numberOfFilesBySubsectionProvider =
    FutureProvider<Future<int>>((ref) async {
  return fetch.fetchNumberOfFilesBySubsection(
      ref.watch(currentProjectID), ref.watch(currentProjectSubSection));
});

final numberOfFilesByEPublishingSubsectionProvider =
    FutureProvider<Future<int>>((ref) async {
  return fetch.fetchNumberOfFilesByEPublishingSubsection(
      ref.watch(currentEPublishingChapter),
      ref.watch(currentEPublishingSubsection));
});

final notificationsEnabledProvider = StateProvider<Future<bool>>((ref) {
  return fetchMain.fetchNotificationStatus();
});

// I have made a few providers down here just for reading mode cause I can and it feels better this way

final projectDescriptionReaderProvider = StateProvider<String>((ref) {
  return "Your description here...";
});

final projectAuthorNameProvider = StateProvider<String>((ref) {
  return "";
});

final isSubscribedProvider =
    FutureProvider.family<bool, String>((ref, projectId) async {
  // Implementation that checks if the user is subscribed to the project with the given projectId
  // Return true if subscribed, false otherwise.
  return await logic.isSubscribedToProject(projectId);
});

final numberOfSubscribedProjectsByUserProvider =
    FutureProvider<int>((ref) async {
  String? uid = FirebaseAuth.instance.currentUser?.uid;
  if (uid == null) {
    return 0; // Or handle the null case appropriately
  }
  return subscriptionsLogicFetch.fetchNumberOfSubscriptionsByUser(uid);
});

//-- application processing providers --------------------------------------------

final currentApplicationID = StateProvider<String>((ref) {
  return "";
});

final numberOfApplicationsByProject = FutureProvider<Future<int>>((ref) async {
  return questionnaire
      .fetchNumberOfApplicationsbyProject(ref.watch(currentProjectID));
});

final numberOfProjectsWithApplications =
    FutureProvider<Future<int>>((ref) async {
  return questionnaire.fetchNumberOfProjectsWithApplications();
});

final numberOfQuestionsByProjectProvider =
    FutureProvider<Future<int>>((ref) async {
  return questionnaireLogic.fetchNumberOfQuestions(ref.watch(currentProjectID));
});
