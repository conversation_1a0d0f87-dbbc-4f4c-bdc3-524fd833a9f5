import 'package:flutter/material.dart';

class UniversalWidgets {
  Widget buildButtonFlat(String buttonText, bool isEnabled,
      VoidCallback onPressedAction, Color backgroundColor, Color textColor) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextButton(
        onPressed: isEnabled ? onPressedAction : null,
        style: TextButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(2.0), // Less rounded corners
          ),
        ),
        child: Text(buttonText),
      ),
    );
  }

  Widget buildButtonFlatWidth(
    String buttonText,
    bool isEnabled,
    VoidCallback onPressedAction,
    Color backgroundColor,
    Color textColor,
    BuildContext context, // Add BuildContext parameter to access MediaQuery
    double widthFactor, // New parameter to specify the width percentage
  ) {
    return Align(
      alignment: Alignment.center,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        width: MediaQuery.of(context).size.width *
            widthFactor, // Use the widthFactor here
        child: TextButton(
          onPressed: isEnabled ? onPressedAction : null,
          style: TextButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: textColor,
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(2.0), // Less rounded corners
            ),
          ),
          child: Text(buttonText),
        ),
      ),
    );
  }

  Widget buildStyledButton(
    String buttonText,
    bool isEnabled,
    VoidCallback onPressedAction,
    Color backgroundColor,
    Color textColor,
    BuildContext context,
    double widthFactor, {
    double borderRadius = 8.0,
    IconData? icon,
    double elevation = 2.0,
  }) {
    return Align(
      alignment: Alignment.center,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        width: MediaQuery.of(context).size.width * widthFactor,
        child: ElevatedButton(
          onPressed: isEnabled ? onPressedAction : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: textColor,
            elevation: elevation,
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Icon(icon, size: 18),
                const SizedBox(width: 8),
              ],
              Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildMinimalistField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,
            style: const TextStyle(
                color: Colors.black87, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        Text(value, style: const TextStyle(color: Colors.black54)),
        const Divider(),
      ],
    );
  }

  Widget buildEditableMinimalistField(String label,
      TextEditingController controller, String defaultValue, double height,
      {int minLines = 1, int? maxLines}) {
    // Check if the controller is empty and set it to the default value
    if (controller.text.isEmpty) {
      controller.text = defaultValue;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,
            style: const TextStyle(
                color: Colors.black87, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        TextField(
          controller: controller,
          minLines: minLines, // Set the minimum lines for the TextField
          maxLines: maxLines, // Allow it to expand to accommodate more lines
          keyboardType:
              TextInputType.multiline, // Set keyboard type for multiline
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            isDense: true, // Reduces the field's height
          ),
        ),
        const Divider(),
      ],
    );
  }

// uses hint text within the field which goes away once user starts typing
  Widget buildEditableMinimalistFieldHintText(String label,
      TextEditingController controller, String hintText, double height,
      {int minLines = 1, int? maxLines}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,
            style: const TextStyle(
                color: Colors.black87, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        TextField(
          controller: controller,
          minLines: minLines, // Set the minimum lines for the TextField
          maxLines: maxLines, // Allow it to expand to accommodate more lines
          keyboardType:
              TextInputType.multiline, // Set keyboard type for multiline
          decoration: InputDecoration(
            hintText: hintText, // Use hintText to show the placeholder
            border: const OutlineInputBorder(),
            isDense: true, // Reduces the field's height
          ),
        ),
        const Divider(),
      ],
    );
  }

  Widget buildEditableMinimalistFieldHintTextToolTipTypeAnswer(
      String label, TextEditingController controller, String hintText,
      {int minLines = 1, int? maxLines}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,
            style: const TextStyle(
                color: Colors.black87, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        TextField(
          controller: controller,
          minLines: minLines,
          maxLines: maxLines ??
              5, // Allows for expansion, sets a reasonable max if not provided
          keyboardType: TextInputType.multiline,
          decoration: InputDecoration(
            hintText: hintText,
            border: const OutlineInputBorder(),
            isDense: true, // Keeps the field compact
            suffixIcon: hintText.length > 10
                ? Tooltip(
                    // Only show if hint text is long
                    message: hintText,
                    child: const Icon(Icons.info_outline, color: Colors.grey),
                  )
                : null,
          ),
        ),
        const Divider(),
      ],
    );
  }

  Widget buildEditableMinimalistFieldHintTextToolTipReadAnswer(
      String label, TextEditingController controller, String initialValue,
      {int minLines = 1, int? maxLines}) {
    bool showToolTip =
        label.length > 20; // Condition to show tooltip based on label length

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        showToolTip
            ? Tooltip(
                message: label,
                child: Text(label,
                    style: const TextStyle(
                        color: Colors.black87, fontWeight: FontWeight.bold)),
              )
            : Text(label,
                style: const TextStyle(
                    color: Colors.black87, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: initialValue,
            border: const OutlineInputBorder(),
          ),
          readOnly: true,
          minLines: minLines,
          maxLines:
              maxLines, // Allow the TextField to expand to accommodate content
        ),
        const Divider(),
      ],
    );
  }

  Widget buildProfilePicture(String initials, Color backgroundColor) {
    return CircleAvatar(
      backgroundColor: backgroundColor,
      radius: 40.0,
      child: Text(
        initials.substring(0, 1),
        style: const TextStyle(fontSize: 40.0, color: Colors.white),
      ),
    );
  }

  Widget buildColorfulCard(
      BuildContext context, String title, VoidCallback onTap) {
    Color bookBranchGreen = const Color.fromARGB(255, 44, 148, 44);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white, // White background
          borderRadius: BorderRadius.zero,
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(25, 0, 0, 0),
              spreadRadius: 2,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: bookBranchGreen, // Thin green outline
            width: 1, // Thickness of the outline
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 8,
              left: 8,
              child: Text(
                title,
                style: TextStyle(
                  color: bookBranchGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildColorfulCardWithQuestion(
      BuildContext context, String title, String message, VoidCallback onTap) {
    Color bookBranchGreen = const Color.fromARGB(255, 44, 148, 44);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white, // White background
          borderRadius: BorderRadius.zero,
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(25, 0, 0, 0),
              spreadRadius: 2,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: bookBranchGreen, // Thin green outline
            width: 1, // Thickness of the outline
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 8,
              left: 8,
              child: Text(
                title,
                style: TextStyle(
                  color: bookBranchGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: IconButton(
                icon: const Icon(Icons.help_outline),
                color: bookBranchGreen,
                onPressed: () {
                  // Handle the help button press
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text(title),
                        content: Text(message),
                        actions: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text("Close"),
                          ),
                        ],
                      );
                    },
                  );
                },
                tooltip: 'Help',
                splashRadius: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildColorfulCardWithSubtitle(
      BuildContext context, String title, String subtitle, VoidCallback onTap) {
    Color bookBranchGreen = const Color.fromARGB(255, 44, 148, 44);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.all(8), // Added padding inside the container
        decoration: BoxDecoration(
          color: Colors.white, // White background
          borderRadius: BorderRadius.zero,
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(25, 0, 0, 0),
              spreadRadius: 2,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: bookBranchGreen, // Thin green outline
            width: 1, // Thickness of the outline
          ),
        ),
        child: Column(
          crossAxisAlignment:
              CrossAxisAlignment.start, // Align text to the start
          children: [
            Text(
              title,
              style: TextStyle(
                color: bookBranchGreen,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
              textAlign: TextAlign.left,
            ),
            const SizedBox(height: 4), // Space between title and subtitle
            Text(
              subtitle,
              style: const TextStyle(
                color:
                    Colors.grey, // Subtitle in grey to distinguish from title
                fontSize: 14, // Smaller font size for subtitle
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildEnhancedColorfulCard(
      BuildContext context, String title, String infoText, VoidCallback onTap,
      {IconData? customIcon}) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    // Default icons based on title if no custom icon is provided
    IconData cardIcon;
    if (customIcon != null) {
      cardIcon = customIcon;
    } else {
      switch (title) {
        case "Trunk":
          cardIcon = Icons.menu_book;
          break;
        case "Branches":
          cardIcon = Icons.source;
          break;
        case "Community":
          cardIcon = Icons.forum;
          break;
        default:
          cardIcon = Icons.article;
      }
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(25, 0, 0, 0),
              spreadRadius: 1,
              blurRadius: 6,
              offset: Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: bookBranchGreen,
            width: 1.5, // Thicker border
          ),
        ),
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 8,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148, 44),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 8,
              right: 0,
              child: Container(
                height: 30,
                width: 30,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                  ),
                ),
              ),
            ),
            // Section icon
            Positioned(
              top: 50,
              left: 16,
              child: Icon(
                cardIcon,
                size: 40,
                color: bookBranchGreen,
              ),
            ),
            // Title text
            Positioned(
              bottom: 16,
              left: 16,
              child: Text(
                title,
                style: const TextStyle(
                  color: bookBranchGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            // Information icon
            Positioned(
              top: 12,
              right: 12,
              child: IconButton(
                icon: const Icon(
                  Icons.info_outline,
                  size: 20,
                ),
                color: bookBranchGreen,
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext dialogContext) {
                      return AlertDialog(
                        title: Text(
                          title,
                          style: const TextStyle(
                            color: Colors.black,
                          ),
                        ),
                        backgroundColor: Colors.white,
                        shape: const RoundedRectangleBorder(
                          side: BorderSide(color: Colors.black, width: 1.0),
                          borderRadius: BorderRadius.zero,
                        ),
                        content: SingleChildScrollView(
                          child: ListBody(
                            children: <Widget>[
                              Text(
                                infoText,
                                style: const TextStyle(
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                        actions: <Widget>[
                          TextButton(
                            child: const Text(
                              "Close",
                              style: TextStyle(
                                color: bookBranchGreen,
                              ),
                            ),
                            onPressed: () {
                              Navigator.of(dialogContext).pop();
                            },
                          ),
                        ],
                      );
                    },
                  );
                },
                tooltip: 'Information',
                splashRadius: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showCustomPopup(
      BuildContext context, String message, String buttonText) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: const RoundedRectangleBorder(
            side: BorderSide(color: bookBranchGreen, width: 1.5),
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
          ),
          elevation: 3,
          child: Container(
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  message,
                  style: const TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.normal,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(dialogContext).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: bookBranchGreen,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6.0),
                      ),
                      elevation: 1,
                    ),
                    child: Text(
                      buttonText,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showCustomPopupWithText(BuildContext context, String title,
      String message, VoidCallback onPressedAction) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.black87,
            ),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
          contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    onPressedAction(); // Call the function passed as parameter
                    Navigator.of(dialogContext)
                        .pop(); // Simply close the dialog
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    elevation: 1,
                  ),
                  child: const Text(
                    'OK',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: bookBranchGreen, width: 1.5),
            borderRadius: BorderRadius.circular(8.0),
          ),
          backgroundColor: Colors.white,
          elevation: 3,
        );
      },
    );
  }

  void showCustomPopupWithTextfield(
      BuildContext context,
      String title,
      String message,
      TextEditingController textEditingController,
      VoidCallback onPressedAction) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.black87,
            ),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
          contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: textEditingController,
                decoration: InputDecoration(
                  labelText: message,
                  hintText: "Start Typing...",
                  labelStyle: const TextStyle(color: Colors.grey),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: bookBranchGreen),
                  ),
                ),
                style: const TextStyle(fontSize: 16),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    onPressedAction(); // Call the function passed as parameter
                    Navigator.of(dialogContext).pop(textEditingController
                        .text); // Pass back data from TextField
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    elevation: 1,
                  ),
                  child: const Text(
                    'OK',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: bookBranchGreen, width: 1.5),
            borderRadius: BorderRadius.circular(8.0),
          ),
          backgroundColor: Colors.white,
          elevation: 3,
        );
      },
    );
  }

  void createDiscussionPopup(BuildContext context, List<String> messages,
      List<TextEditingController> controllers, VoidCallback onPressedAction) {
    assert(messages.length == controllers.length,
        'There must be an equal number of messages and text controllers.');

    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text(
            'New Discussion',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.black87,
            ),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
          contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                for (int i = 0; i < messages.length; i++) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Text(
                      messages[i],
                      style: const TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  TextField(
                    controller: controllers[i],
                    decoration: InputDecoration(
                      labelText: "Enter ${messages[i]}",
                      labelStyle: const TextStyle(color: Colors.grey),
                      enabledBorder: const UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: const UnderlineInputBorder(
                        borderSide: BorderSide(color: bookBranchGreen),
                      ),
                    ),
                    style: const TextStyle(fontSize: 16),
                    maxLines: 1,
                  ),
                ],
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 10),
                  child: Text(
                    'Attach Media',
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: InkWell(
                    onTap: () {
                      onPressedAction();
                    },
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                      child: Row(
                        children: [
                          Icon(Icons.attach_file, color: Colors.grey),
                          SizedBox(width: 16),
                          Text(
                            'Attach Media',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      onPressedAction();
                      for (var controller in controllers) {
                        controller.clear();
                      }
                      Navigator.of(dialogContext).pop(); // Close the dialog
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: bookBranchGreen,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6.0),
                      ),
                      elevation: 1,
                    ),
                    child: const Text(
                      'OK',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ),
          ),
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: bookBranchGreen, width: 1.5),
            borderRadius: BorderRadius.circular(8.0),
          ),
          backgroundColor: Colors.white,
          elevation: 3,
        );
      },
    );
  }

  void showCustomPopupWithCheckbox({
    required BuildContext context,
    required String titleText,
    required String contentText,
    required String checkboxText,
    required String cancelButtonText,
    required String actionButtonText,
    required VoidCallback onActionPressed,
  }) {
    bool isChecked = false;
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                titleText,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                  color: Colors.black87,
                ),
              ),
              titlePadding: const EdgeInsets.fromLTRB(24.0, 24.0, 24.0, 0.0),
              contentPadding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 24.0),
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                side: const BorderSide(color: bookBranchGreen, width: 1.5),
                borderRadius: BorderRadius.circular(8.0),
              ),
              elevation: 3,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    contentText,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade200),
                      borderRadius: BorderRadius.circular(6.0),
                      color: Colors.grey.shade50,
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    child: Row(
                      children: [
                        Checkbox(
                          value: isChecked,
                          onChanged: (bool? value) {
                            setState(() {
                              isChecked = value!;
                            });
                          },
                          activeColor: bookBranchGreen,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            checkboxText,
                            style: const TextStyle(
                              color: Colors.black87,
                              fontSize: 15,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  child: Text(
                    cancelButtonText,
                    style: const TextStyle(
                      color: bookBranchGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                ),
                ElevatedButton(
                  onPressed: isChecked
                      ? () {
                          Navigator.of(dialogContext).pop();
                          onActionPressed();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: bookBranchGreen,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey.shade300,
                    disabledForegroundColor: Colors.grey.shade600,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    elevation: 1,
                  ),
                  child: Text(
                    actionButtonText,
                    style: const TextStyle(
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
              actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            );
          },
        );
      },
    );
  }
}
