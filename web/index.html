<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="web_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>web_app</title>
  <link rel="manifest" href="manifest.json">

<<<<<<< HEAD
  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = null;
=======
  <!-- web packages -->
  <script src="https://www.gstatic.com/firebasejs/10.6.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.6.0/firebase-analytics.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.2.0/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.2.0/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.2.0/firebase-storage.js"></script>

<script>


  

// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAu5WZ-pGt8aWdENUrBHhBBmMIQfEww9xM",
  authDomain: "bookbox-5153e.firebaseapp.com",
  projectId: "bookbox-5153e",
  storageBucket: "bookbox-5153e.appspot.com",
  messagingSenderId: "497277241320",
  appId: "1:497277241320:web:fb931efe7c19f24292065d",
  measurementId: "G-MJ26BFS0WY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);


</script>


<!-- PDF stuff-->
<script src="//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.4.456/pdf.min.js"></script>

<script type="text/javascript">
       pdfjsLib.GlobalWorkerOptions.workerSrc = "//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.4.456/pdf.worker.min.js";
    </script>
<!-- End PDF stuff-->


  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
>>>>>>> 91d1ec4b9cc208ea953c64f8d4327bc5a5b9bb4a
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
</head>
<body>
<<<<<<< HEAD
=======
  <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@2.12.313/build/pdf.js" type="text/javascript"></script>
  <script type="text/javascript">
    pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdn.jsdelivr.net/npm/pdfjs-dist@2.12.313/build/pdf.worker.min.js";
    pdfRenderOptions = {
      cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.12.313/cmaps/',
      cMapPacked: true,
    }
  </script>  
>>>>>>> 91d1ec4b9cc208ea953c64f8d4327bc5a5b9bb4a
  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>
</html>
