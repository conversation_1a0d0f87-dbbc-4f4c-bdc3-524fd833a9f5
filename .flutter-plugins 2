# This is a generated file; do not edit or check into version control.
cloud_firestore=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.12.2/
cloud_firestore_web=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.8.3/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.1/
file_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-6.1.1/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/
firebase_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.12.1/
firebase_auth_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.6/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.22.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.8.1/
firebase_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.5.1/
firebase_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.6.13/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-5.4.4/
google_sign_in_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.21/
google_sign_in_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.6.3/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.10.2+1/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-0.8.9/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.9+3/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.9+1/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
pdfx=/Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.5.0/
video_player=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.7.2/
video_player_android=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.4.12/
video_player_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.4.11/
video_player_web=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.0.17/
