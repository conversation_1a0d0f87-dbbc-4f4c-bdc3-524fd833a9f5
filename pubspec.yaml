name: web_app
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.30.0
  easy_sidemenu: ^0.6.0
  video_player: ^2.7.0
  chewie: ^1.8.1
  video_player_web: ^2.0.4
  image_picker: ^1.0.8
  google_sign_in: ^6.2.1
  firebase_app_check: ^0.2.2+5
  intl: ^0.19.0
  pdfx: ^2.6.0
  firebase_messaging: ^14.9.2
  photo_view: ^0.14.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  firebase_auth: ^4.6.3
  cloud_firestore: ^4.8.2
  #riverpod
  flutter_riverpod: ^2.4.5

  riverpod_annotation: ^2.3.0
  firebase_storage: ^11.7.1
  file_picker: ^8.0.0+1
  tuple: ^2.0.0
  loading_indicator: ^3.1.1
  url_launcher: ^6.3.0
  cloud_functions: ^4.7.4
  http: ^1.2.2
  purchases_flutter: ^8.4.0
  purchases_ui_flutter: ^8.4.0
  crypto: ^3.0.6

  # UI
  getwidget: ^6.0.0
  flutter_login: ^5.0.0
  animated_text_kit: ^4.2.3   # latest stable, Mar 2025 :contentReference[oaicite:0]{index=0}
  dynamic_background: ^0.1.3 # this is a very old version in reality 
  permission_handler: ^12.0.0+1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.2
  #riverpod
  riverpod_generator: ^2.3.5
  riverpod_lint: ^2.3.3
  flutter_launcher_icons: ^0.13.1


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
      # logo
      - assets/google/signin_logo.png
      - assets/pdf_icon.png
      - assets/play_image.png
      - assets/default_thumbnail.png
      - assets/logo/launcher_icon.png
      - assets/logo/launcher_icon_ios.png
      - assets/green_background.png
      - assets/signInBackground.png


flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/logo/launcher_icon_ios.png"

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
