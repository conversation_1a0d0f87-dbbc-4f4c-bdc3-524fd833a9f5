{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.29", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "analyzer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "analyzer_plugin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer_plugin-0.11.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "badges", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/badges-3.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "build", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "build_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "ci", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ci-0.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cli_util", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cloud_firestore", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.16.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "cloud_firestore_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.1.13", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "cloud_firestore_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.11.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "custom_lint", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint-0.6.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "custom_lint_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_builder-0.6.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "custom_lint_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_core-0.6.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dart_style", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "easy_sidemenu", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/easy_sidemenu-0.6.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "extension", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/extension-0.6.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.0+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "firebase_auth", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.19.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.2.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_auth_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.11.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.30.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.0.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.15.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.7.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_storage_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_platform_interface-5.1.16", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_storage_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.9.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.19", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_riverpod", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.5.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/Documents/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "freezed_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "glob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "google_identity_services_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.1+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_sign_in", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "google_sign_in_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.23", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "google_sign_in_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.4.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "google_sign_in_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hotreloader-4.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.9+6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.9+2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.8.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-2.0.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-2.0.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.11.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "package_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pdfx", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.6.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "photo_view", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.14.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pub_semver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.1.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "pubspec_parse", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.2.3", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "riverpod", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.5.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "riverpod_analyzer_utils", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_analyzer_utils-0.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "riverpod_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.3.5", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "riverpod_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_generator-2.4.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "riverpod_lint", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_lint-2.3.10", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/Documents/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_gen", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "state_notifier", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "universal_platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.0.0+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "video_player", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.8.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "video_player_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.4.13", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "video_player_avfoundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.5.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "video_player_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.2.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "video_player_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-13.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "watcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.4.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "web_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.0"}], "generated": "2024-04-28T02:20:08.848065Z", "generator": "pub", "generatorVersion": "3.3.3"}