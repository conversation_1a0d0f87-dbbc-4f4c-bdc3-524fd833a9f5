1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.web_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="19"
9        android:targetSdkVersion="33" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:6:5-66
15-->/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:6:22-64
16    <uses-permission
16-->/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:2:1-75
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:2:18-73
18        android:maxSdkVersion="32" />
18-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:11:9-35
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:3:1-77
19-->/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:3:18-74
20
21    <queries>
21-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-19:15
22        <intent>
22-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-18:18
23            <action android:name="android.intent.action.GET_CONTENT" />
23-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-72
23-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:15:21-69
24
25            <data android:mimeType="*/*" />
25-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-44
25-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:17:19-41
26        </intent>
27    </queries>
28
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:5-79
29-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:22-76
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:24:5-68
30-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:24:22-65
31    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
31-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:25:5-79
31-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:25:22-76
32    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
32-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:26:5-88
32-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:26:22-85
33    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
33-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:27:5-82
33-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:27:22-79
34    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
34-->[com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:5-98
34-->[com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:22-95
35    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
35-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:26:5-110
35-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:26:22-107
36
37    <permission
37-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
38        android:name="com.example.web_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.web_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="io.flutter.app.FlutterMultiDexApplication"
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:icon="@mipmap/ic_launcher"
48        android:label="web_app" >
49        <activity
50            android:name="com.example.web_app.MainActivity"
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
52            android:exported="true"
53            android:hardwareAccelerated="true"
54            android:launchMode="singleTop"
55            android:theme="@style/LaunchTheme"
56            android:windowSoftInputMode="adjustResize" >
57
58            <!--
59                 Specifies an Android theme to apply to this Activity as soon as
60                 the Android process has started. This theme is visible to the user
61                 while the Flutter UI initializes. After that, this theme continues
62                 to determine the Window background behind the Flutter UI.
63            -->
64            <meta-data
65                android:name="io.flutter.embedding.android.NormalTheme"
66                android:resource="@style/NormalTheme" />
67
68            <intent-filter>
69                <action android:name="android.intent.action.MAIN" />
70
71                <category android:name="android.intent.category.LAUNCHER" />
72            </intent-filter>
73        </activity>
74        <!--
75             Don't delete the meta-data below.
76             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
77        -->
78        <meta-data
79            android:name="flutterEmbedding"
80            android:value="2" />
81
82        <service
82-->[:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
83            android:name="com.google.firebase.components.ComponentDiscoveryService"
83-->[:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:10:18-89
84            android:directBootAware="true"
84-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:32:13-43
85            android:exported="false" >
85-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:68:13-37
86            <meta-data
86-->[:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
87                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
87-->[:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-124
88                android:value="com.google.firebase.components.ComponentRegistrar" />
88-->[:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
89            <meta-data
89-->[:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
90                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
90-->[:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-126
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
92            <meta-data
92-->[:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
93                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
93-->[:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-134
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
95            <meta-data
95-->[:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
96                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
96-->[:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-124
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
98            <meta-data
98-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:69:13-71:85
99                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
99-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:70:17-109
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:71:17-82
101            <meta-data
101-->[com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:30:13-32:85
102                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
102-->[com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:31:17-118
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:32:17-82
104            <meta-data
104-->[com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:33:13-35:85
105                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
105-->[com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:34:17-107
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:35:17-82
107            <meta-data
107-->[com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:17:13-19:85
108                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
108-->[com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:18:17-122
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:19:17-82
110            <meta-data
110-->[com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:20:13-22:85
111                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
111-->[com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:21:17-111
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:22:17-82
113            <meta-data
113-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:37:13-39:85
114                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
114-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:38:17-139
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:39:17-82
116            <meta-data
116-->[com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:25:13-27:85
117                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
117-->[com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:26:17-120
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:27:17-82
119            <meta-data
119-->[com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:28:13-30:85
120                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
120-->[com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:29:17-117
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:30:17-82
122            <meta-data
122-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
123                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
123-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
125            <meta-data
125-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
126                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
126-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
128            <meta-data
128-->[com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:12:13-14:85
129                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
129-->[com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:13:17-116
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:14:17-82
131            <meta-data
131-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:35:13-37:85
132                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
132-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:36:17-109
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:37:17-82
134        </service>
135
136        <provider
136-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-18:20
137            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
137-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-82
138            android:authorities="com.example.web_app.flutter.image_provider"
138-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-74
139            android:exported="false"
139-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-37
140            android:grantUriPermissions="true" >
140-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-47
141            <meta-data
141-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-17:75
142                android:name="android.support.FILE_PROVIDER_PATHS"
142-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:17-67
143                android:resource="@xml/flutter_image_picker_file_paths" />
143-->[:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-72
144        </provider>
145
146        <activity
146-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:29:9-46:20
147            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
147-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:30:13-80
148            android:excludeFromRecents="true"
148-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:31:13-46
149            android:exported="true"
149-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:32:13-36
150            android:launchMode="singleTask"
150-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:33:13-44
151            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
151-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:34:13-72
152            <intent-filter>
152-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:35:13-45:29
153                <action android:name="android.intent.action.VIEW" />
153-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
153-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
154
155                <category android:name="android.intent.category.DEFAULT" />
155-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:17-76
155-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:27-73
156                <category android:name="android.intent.category.BROWSABLE" />
156-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
156-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
157
158                <data
158-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-44
159                    android:host="firebase.auth"
160                    android:path="/"
161                    android:scheme="genericidp" />
162            </intent-filter>
163        </activity>
164        <activity
164-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:47:9-64:20
165            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
165-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:48:13-79
166            android:excludeFromRecents="true"
166-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:49:13-46
167            android:exported="true"
167-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:50:13-36
168            android:launchMode="singleTask"
168-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:51:13-44
169            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
169-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:52:13-72
170            <intent-filter>
170-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:53:13-63:29
171                <action android:name="android.intent.action.VIEW" />
171-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
171-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
172
173                <category android:name="android.intent.category.DEFAULT" />
173-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:17-76
173-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:27-73
174                <category android:name="android.intent.category.BROWSABLE" />
174-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
174-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
175
176                <data
176-->[:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-44
177                    android:host="firebase.auth"
178                    android:path="/"
179                    android:scheme="recaptcha" />
180            </intent-filter>
181        </activity>
182
183        <property
183-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:30:9-32:61
184            android:name="android.adservices.AD_SERVICES_CONFIG"
184-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:31:13-65
185            android:resource="@xml/ga_ad_services_config" />
185-->[com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:32:13-58
186
187        <provider
187-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:23:9-28:39
188            android:name="com.google.firebase.provider.FirebaseInitProvider"
188-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:24:13-77
189            android:authorities="com.example.web_app.firebaseinitprovider"
189-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:25:13-72
190            android:directBootAware="true"
190-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:26:13-43
191            android:exported="false"
191-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:27:13-37
192            android:initOrder="100" />
192-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:28:13-36
193
194        <activity
194-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:23:9-27:75
195            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
195-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:24:13-93
196            android:excludeFromRecents="true"
196-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:25:13-46
197            android:exported="false"
197-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:26:13-37
198            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
198-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:27:13-72
199        <!--
200            Service handling Google Sign-In user revocation. For apps that do not integrate with
201            Google Sign-In, this service will never be started.
202        -->
203        <service
203-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:33:9-37:51
204            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
204-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:34:13-89
205            android:exported="true"
205-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:35:13-36
206            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
206-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:36:13-107
207            android:visibleToInstantApps="true" />
207-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:37:13-48
208
209        <receiver
209-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:29:9-33:20
210            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
210-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:30:13-85
211            android:enabled="true"
211-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:31:13-35
212            android:exported="false" >
212-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:32:13-37
213        </receiver>
214
215        <service
215-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:35:9-38:40
216            android:name="com.google.android.gms.measurement.AppMeasurementService"
216-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:36:13-84
217            android:enabled="true"
217-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:37:13-35
218            android:exported="false" />
218-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:38:13-37
219        <service
219-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:39:9-43:72
220            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
220-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:40:13-87
221            android:enabled="true"
221-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:41:13-35
222            android:exported="false"
222-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:42:13-37
223            android:permission="android.permission.BIND_JOB_SERVICE" />
223-->[com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:43:13-69
224
225        <activity
225-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:9-22:45
226            android:name="com.google.android.gms.common.api.GoogleApiActivity"
226-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:19-85
227            android:exported="false"
227-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:22:19-43
228            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
228-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:21:19-78
229
230        <meta-data
230-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
231            android:name="com.google.android.gms.version"
231-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
232            android:value="@integer/google_play_services_version" />
232-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
233
234        <uses-library
234-->[androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:25:9-27:40
235            android:name="androidx.window.extensions"
235-->[androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:26:13-54
236            android:required="false" />
236-->[androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:27:13-37
237        <uses-library
237-->[androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:28:9-30:40
238            android:name="androidx.window.sidecar"
238-->[androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:29:13-51
239            android:required="false" />
239-->[androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:30:13-37
240        <uses-library
240-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
241            android:name="android.ext.adservices"
241-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
242            android:required="false" />
242-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
243
244        <provider
244-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
245            android:name="androidx.startup.InitializationProvider"
245-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:25:13-67
246            android:authorities="com.example.web_app.androidx-startup"
246-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:26:13-68
247            android:exported="false" >
247-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:27:13-37
248            <meta-data
248-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
249                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
249-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
250                android:value="androidx.startup" />
250-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
251        </provider>
252
253        <receiver
253-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
254            android:name="androidx.profileinstaller.ProfileInstallReceiver"
254-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
255            android:directBootAware="false"
255-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
256            android:enabled="true"
256-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
257            android:exported="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
258            android:permission="android.permission.DUMP" >
258-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
260                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
260-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
263                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
263-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
266                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
266-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
269                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
269-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
269-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
270            </intent-filter>
271        </receiver>
272    </application>
273
274</manifest>
