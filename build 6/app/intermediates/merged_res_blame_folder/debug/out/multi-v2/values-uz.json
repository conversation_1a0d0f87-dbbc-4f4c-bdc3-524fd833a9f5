{"logs": [{"outputFile": "com.example.web_app-mergeDebugResources-29:/values-uz/values-uz.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,4163", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,4259"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "3032,3838,3947,4057", "endColumns": "117,108,109,105", "endOffsets": "3145,3942,4052,4158"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3150,3215,3281,3351,3415,3494,3562,3664,3758", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "3210,3276,3346,3410,3489,3557,3659,3753,3833"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/res/values-uz/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,896,1046,1175,1284,1429,1562,1682,1938,2110,2218,2377,2509,2663,2825,2891,2952", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "891,1041,1170,1279,1424,1557,1677,1783,2105,2213,2372,2504,2658,2820,2886,2947,3027"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/res/values-uz/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1788", "endColumns": "149", "endOffsets": "1933"}}]}]}