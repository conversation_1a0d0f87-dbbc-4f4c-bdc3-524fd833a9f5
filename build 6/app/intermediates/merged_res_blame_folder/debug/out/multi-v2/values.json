{"logs": [{"outputFile": "com.example.web_app-mergeDebugResources-29:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "144,145,146,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10133,10203,10265,10330,10394,10471,10536,10626,10710", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "10198,10260,10325,10389,10466,10531,10621,10705,10774"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/30c0766264951ffb9c5db8cf2344b538/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "6832", "endColumns": "53", "endOffsets": "6881"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/14ac4d8b22d77d4a235e2eedc62708fb/transformed/lifecycle-runtime-2.6.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6729", "endColumns": "42", "endOffsets": "6767"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/res/values/values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "3,8,9,10,88,177,183,337,345,357", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "165,344,404,456,5576,12312,12507,17454,17736,18176", "endLines": "7,8,9,10,88,182,186,344,356,364", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "339,399,451,496,5631,12502,12633,17731,18171,18480"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/0796421eeb6f75478acb94c1f7a523c6/transformed/jetified-activity-1.7.2/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "92,111", "startColumns": "4,4", "startOffsets": "5763,6772", "endColumns": "41,59", "endOffsets": "5800,6827"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/7f76562c96aa478b1aa5042180ea825c/transformed/fragment-1.5.7/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "89,93,114,257,262", "startColumns": "4,4,4,4,4", "startOffsets": "5636,5805,6936,15554,15724", "endLines": "89,93,114,261,265", "endColumns": "56,64,63,24,24", "endOffsets": "5688,5865,6995,15719,15868"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "117", "startColumns": "4", "startOffsets": "7138", "endColumns": "82", "endOffsets": "7216"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "19,20,21,22,23,24,25,26,125,126,127,128,129,130,131,132,134,135,136,137,138,139,140,141,142,305,318", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1041,1131,1211,1301,1391,1471,1552,1632,7732,7837,8018,8143,8250,8430,8553,8669,8939,9127,9232,9413,9538,9713,9861,9924,9986,16722,17037", "endLines": "19,20,21,22,23,24,25,26,125,126,127,128,129,130,131,132,134,135,136,137,138,139,140,141,142,317,336", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1126,1206,1296,1386,1466,1547,1627,1707,7832,8013,8138,8245,8425,8548,8664,8767,9122,9227,9408,9533,9708,9856,9919,9981,10060,17032,17449"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/f577177c9d6a2b56675f36ac9bbf280e/transformed/lifecycle-viewmodel-2.6.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6886", "endColumns": "49", "endOffsets": "6931"}}, {"source": "/Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "157,161", "startColumns": "4,4", "startOffsets": "11065,11246", "endLines": "160,163", "endColumns": "12,12", "endOffsets": "11241,11410"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "13,14,15,16,29,30,143,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "661,719,785,848,1844,1915,10065,10779,10846,10925", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "714,780,843,905,1910,1982,10128,10841,10920,10989"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,11,12,17,18,27,28,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,116,118,119,120,121,122,123,124,156,164,165,169,170,174,175,176,187,193,203,236,266,299", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,501,573,910,975,1712,1781,1987,2057,2125,2197,2267,2328,2402,2475,2536,2597,2659,2723,2785,2846,2914,3014,3074,3140,3213,3282,3339,3391,3453,3525,3601,3666,3725,3784,3844,3904,3964,4024,4084,4144,4204,4264,4324,4384,4443,4503,4563,4623,4683,4743,4803,4863,4923,4983,5043,5102,5162,5222,5281,5340,5399,5458,5517,5693,5728,5870,5925,5988,6043,6101,6159,6220,6283,6340,6391,6441,6502,6559,6625,6659,6694,7068,7221,7288,7360,7429,7498,7572,7644,10994,11415,11532,11733,11843,12044,12173,12245,12638,12841,13142,14873,15873,16555", "endLines": "2,11,12,17,18,27,28,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,116,118,119,120,121,122,123,124,156,164,168,169,173,174,175,176,192,202,235,256,298,304", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "160,568,656,970,1036,1776,1839,2052,2120,2192,2262,2323,2397,2470,2531,2592,2654,2718,2780,2841,2909,3009,3069,3135,3208,3277,3334,3386,3448,3520,3596,3661,3720,3779,3839,3899,3959,4019,4079,4139,4199,4259,4319,4379,4438,4498,4558,4618,4678,4738,4798,4858,4918,4978,5038,5097,5157,5217,5276,5335,5394,5453,5512,5571,5723,5758,5920,5983,6038,6096,6154,6215,6278,6335,6386,6436,6497,6554,6620,6654,6689,6724,7133,7283,7355,7424,7493,7567,7639,7727,11060,11527,11728,11838,12039,12168,12240,12307,12836,13137,14868,15549,16550,16717"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "115,133", "startColumns": "4,4", "startOffsets": "7000,8772", "endColumns": "67,166", "endOffsets": "7063,8934"}}]}]}