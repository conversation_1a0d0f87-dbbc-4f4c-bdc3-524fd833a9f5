{"logs": [{"outputFile": "com.example.web_app-mergeDebugResources-29:/values-eu/values-eu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3159,3228,3291,3357,3429,3506,3580,3691,3789", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "3223,3286,3352,3424,3501,3575,3686,3784,3852"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/res/values-eu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,888,1060,1184,1293,1445,1570,1694,1945,2123,2231,2394,2522,2676,2836,2902,2967", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "883,1055,1179,1288,1440,1565,1689,1797,2118,2226,2389,2517,2671,2831,2897,2962,3054"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "3059,3857,3959,4072", "endColumns": "99,101,112,104", "endOffsets": "3154,3954,4067,4172"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,563,666,784", "endColumns": "97,102,99,102,103,102,117,100", "endOffsets": "148,251,351,454,558,661,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,563,666,4177", "endColumns": "97,102,99,102,103,102,117,100", "endOffsets": "148,251,351,454,558,661,779,4273"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/res/values-eu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1802", "endColumns": "142", "endOffsets": "1940"}}]}]}