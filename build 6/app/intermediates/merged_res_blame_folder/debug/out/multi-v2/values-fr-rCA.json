{"logs": [{"outputFile": "com.example.web_app-mergeDebugResources-29:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "3190,4027,4129,4248", "endColumns": "106,101,118,104", "endOffsets": "3292,4124,4243,4348"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "778,884,1064,1194,1303,1474,1607,1728,2002,2197,2309,2494,2630,2790,2969,3042,3106", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "879,1059,1189,1298,1469,1602,1723,1836,2192,2304,2489,2625,2785,2964,3037,3101,3185"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/res/values-fr-rCA/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1841", "endColumns": "160", "endOffsets": "1997"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,4353", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,4449"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3297,3371,3436,3514,3586,3669,3745,3842,3935", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "3366,3431,3509,3581,3664,3740,3837,3930,4022"}}]}]}