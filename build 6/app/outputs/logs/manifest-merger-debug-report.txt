-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:5:5-35:19
MERGED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-19:19
MERGED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-19:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-analytics:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/9553c4447657103e151eba9fd066e4a8/transformed/jetified-firebase-analytics-21.6.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/9553c4447657103e151eba9fd066e4a8/transformed/jetified-firebase-analytics-21.6.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4a58d22038863b29593978acdd68d01/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4a58d22038863b29593978acdd68d01/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/cfd03f01bb0b3a4619eea03bacba9061/transformed/jetified-play-services-measurement-sdk-21.6.1/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/cfd03f01bb0b3a4619eea03bacba9061/transformed/jetified-play-services-measurement-sdk-21.6.1/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ea0f5de5c6b8b63397d6c08baf9c4584/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ea0f5de5c6b8b63397d6c08baf9c4584/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/dd13792b63229d51171bb341b5798eda/transformed/jetified-firebase-appcheck-interop-17.1.0/AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/dd13792b63229d51171bb341b5798eda/transformed/jetified-firebase-appcheck-interop-17.1.0/AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/d8aced025a8bfa689c4c3f1d5183e0a6/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/d8aced025a8bfa689c4c3f1d5183e0a6/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/0c08aabfde7de2a421ea701ae1b20b12/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/0c08aabfde7de2a421ea701ae1b20b12/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/b090b9808bf4d65a339353711930554c/transformed/jetified-play-services-measurement-base-21.6.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/b090b9808bf4d65a339353711930554c/transformed/jetified-play-services-measurement-base-21.6.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/d025905517924d483c1d906dba12e2f9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/d025905517924d483c1d906dba12e2f9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/ed4b0a856f877b851f4bc09cb90ec412/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/ed4b0a856f877b851f4bc09cb90ec412/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9fe4fa9274742482c22e976791085b00/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9fe4fa9274742482c22e976791085b00/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
MERGED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
MERGED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-17:12
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-17:12
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-17:12
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-17:12
MERGED from [:pdfx] /Users/<USER>/Documents/Programming/BookBox/web_app/build/pdfx/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-21:12
MERGED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-21:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Documents/Programming/BookBox/web_app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:device_info_plus] /Users/<USER>/Documents/Programming/BookBox/web_app/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:video_player_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/bac24fa97a4ecf40ffd3a7c277b1bb3b/transformed/multidex-2.0.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-analytics:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/9553c4447657103e151eba9fd066e4a8/transformed/jetified-firebase-analytics-21.6.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4a58d22038863b29593978acdd68d01/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/cfd03f01bb0b3a4619eea03bacba9061/transformed/jetified-play-services-measurement-sdk-21.6.1/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ea0f5de5c6b8b63397d6c08baf9c4584/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/dd13792b63229d51171bb341b5798eda/transformed/jetified-firebase-appcheck-interop-17.1.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/transforms-3/e98c257363e378a27019d48f15a2efe6/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/transforms-3/92a98698489f56313f5f8b4800d349d4/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/d8aced025a8bfa689c4c3f1d5183e0a6/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/8d585cca0e3066660826b98149cdebe3/transformed/jetified-firebase-database-collection-18.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/c7960d98685c947c777c4b8c83694f07/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/0c08aabfde7de2a421ea701ae1b20b12/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/b090b9808bf4d65a339353711930554c/transformed/jetified-play-services-measurement-base-21.6.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/d025905517924d483c1d906dba12e2f9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/ed4b0a856f877b851f4bc09cb90ec412/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/7f76562c96aa478b1aa5042180ea825c/transformed/fragment-1.5.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] /Users/<USER>/.gradle/caches/transforms-3/0796421eeb6f75478acb94c1f7a523c6/transformed/jetified-activity-1.7.2/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/e08b21151795f863763564e148e356ac/transformed/jetified-exoplayer-hls-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/026f9204db63ed69369a3a620fb425db/transformed/jetified-exoplayer-dash-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/501c52351204ba0f4e0611971ead4e18/transformed/jetified-exoplayer-smoothstreaming-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.window:window-java:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/554db18c39cca0979ffc5137148e74d4/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.browser:browser:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d815b2d6341d59a239f55bf97069f94b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/05408a8667aa100cc1bbf729479150a7/transformed/loader-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/8ee000f7c689c203c7300d75450ac5d6/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/55aaf5777235a06512542a72b045154c/transformed/jetified-core-ktx-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2b5038cf32a6108ac7f0cc7e43387035/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a67d3f13690133ff569e5f737bc54e60/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/30c0766264951ffb9c5db8cf2344b538/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/9f2623c0f62b6ccf5381bd0fbcea8598/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/f577177c9d6a2b56675f36ac9bbf280e/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/14ac4d8b22d77d4a235e2eedc62708fb/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/c3a60f3d5b06e7db24933c33e60e5097/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/ba056e60dda6bab90ac471869598eb26/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/649d287516b5661989578d4cebde7789/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/transforms-3/1f961c48afa6d74fe2b97d9d6f812d0b/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/4d60d1fbffe4e1b983e5cc431794c185/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/22419efcbd8d3b33f3ce8432e8d488c1/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9fe4fa9274742482c22e976791085b00/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/16381cb1f12da9192016d2bfdca490f6/transformed/jetified-exoplayer-datasource-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/c5787bcf3352420dc1a99552536e105a/transformed/jetified-exoplayer-extractor-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/0ae9658b5ffe02d9cf7909a656958fc3/transformed/jetified-exoplayer-decoder-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2bee83a12b8347587af90ee7bdb5d837/transformed/jetified-exoplayer-database-2.18.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/697f85aad62134b0b82754019dfe760f/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/d916136341eee04a01bd9bdcfa44eaa8/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f16fafad3502f8ace2464139725fd20c/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/319a802935da1bbd3f801985fd3fd4d0/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/cf17373be7e9273a3ff8c8aa25e41959/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.57.2] /Users/<USER>/.gradle/caches/transforms-3/e560e1f246dc5b9b3d5b180f89d2b798/transformed/jetified-grpc-android-1.57.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2cbb59da91b309ca5269bddaf67dff9/transformed/jetified-protolite-well-known-types-18.0.0/AndroidManifest.xml:2:1-11:12
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
	package
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:1-36:12
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:2:1-75
MERGED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-11:38
MERGED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-11:38
	android:maxSdkVersion
		ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:11:9-35
	android:name
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:2:18-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:3:1-77
	android:name
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml:3:18-74
uses-sdk
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
MERGED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:pdfx] /Users/<USER>/Documents/Programming/BookBox/web_app/build/pdfx/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:pdfx] /Users/<USER>/Documents/Programming/BookBox/web_app/build/pdfx/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Documents/Programming/BookBox/web_app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Documents/Programming/BookBox/web_app/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:device_info_plus] /Users/<USER>/Documents/Programming/BookBox/web_app/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:device_info_plus] /Users/<USER>/Documents/Programming/BookBox/web_app/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:video_player_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:video_player_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/bac24fa97a4ecf40ffd3a7c277b1bb3b/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/bac24fa97a4ecf40ffd3a7c277b1bb3b/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/9553c4447657103e151eba9fd066e4a8/transformed/jetified-firebase-analytics-21.6.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/9553c4447657103e151eba9fd066e4a8/transformed/jetified-firebase-analytics-21.6.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4a58d22038863b29593978acdd68d01/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4a58d22038863b29593978acdd68d01/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/cfd03f01bb0b3a4619eea03bacba9061/transformed/jetified-play-services-measurement-sdk-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/cfd03f01bb0b3a4619eea03bacba9061/transformed/jetified-play-services-measurement-sdk-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ea0f5de5c6b8b63397d6c08baf9c4584/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ea0f5de5c6b8b63397d6c08baf9c4584/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/dd13792b63229d51171bb341b5798eda/transformed/jetified-firebase-appcheck-interop-17.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/dd13792b63229d51171bb341b5798eda/transformed/jetified-firebase-appcheck-interop-17.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/transforms-3/e98c257363e378a27019d48f15a2efe6/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/transforms-3/e98c257363e378a27019d48f15a2efe6/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/transforms-3/92a98698489f56313f5f8b4800d349d4/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/transforms-3/92a98698489f56313f5f8b4800d349d4/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/d8aced025a8bfa689c4c3f1d5183e0a6/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/d8aced025a8bfa689c4c3f1d5183e0a6/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/8d585cca0e3066660826b98149cdebe3/transformed/jetified-firebase-database-collection-18.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/8d585cca0e3066660826b98149cdebe3/transformed/jetified-firebase-database-collection-18.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/c7960d98685c947c777c4b8c83694f07/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/c7960d98685c947c777c4b8c83694f07/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/0c08aabfde7de2a421ea701ae1b20b12/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/0c08aabfde7de2a421ea701ae1b20b12/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/b090b9808bf4d65a339353711930554c/transformed/jetified-play-services-measurement-base-21.6.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/b090b9808bf4d65a339353711930554c/transformed/jetified-play-services-measurement-base-21.6.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/d025905517924d483c1d906dba12e2f9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/d025905517924d483c1d906dba12e2f9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/ed4b0a856f877b851f4bc09cb90ec412/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/ed4b0a856f877b851f4bc09cb90ec412/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/7f76562c96aa478b1aa5042180ea825c/transformed/fragment-1.5.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/7f76562c96aa478b1aa5042180ea825c/transformed/fragment-1.5.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] /Users/<USER>/.gradle/caches/transforms-3/0796421eeb6f75478acb94c1f7a523c6/transformed/jetified-activity-1.7.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] /Users/<USER>/.gradle/caches/transforms-3/0796421eeb6f75478acb94c1f7a523c6/transformed/jetified-activity-1.7.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/e08b21151795f863763564e148e356ac/transformed/jetified-exoplayer-hls-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/e08b21151795f863763564e148e356ac/transformed/jetified-exoplayer-hls-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/026f9204db63ed69369a3a620fb425db/transformed/jetified-exoplayer-dash-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/026f9204db63ed69369a3a620fb425db/transformed/jetified-exoplayer-dash-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/501c52351204ba0f4e0611971ead4e18/transformed/jetified-exoplayer-smoothstreaming-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/501c52351204ba0f4e0611971ead4e18/transformed/jetified-exoplayer-smoothstreaming-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window-java:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/554db18c39cca0979ffc5137148e74d4/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window-java:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/554db18c39cca0979ffc5137148e74d4/transformed/jetified-window-java-1.0.0-beta04/AndroidManifest.xml:19:5-21:41
MERGED from [androidx.browser:browser:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/b1d357970d59be2547504e042124d99c/transformed/browser-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d815b2d6341d59a239f55bf97069f94b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d815b2d6341d59a239f55bf97069f94b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/05408a8667aa100cc1bbf729479150a7/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/05408a8667aa100cc1bbf729479150a7/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/8ee000f7c689c203c7300d75450ac5d6/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/8ee000f7c689c203c7300d75450ac5d6/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/55aaf5777235a06512542a72b045154c/transformed/jetified-core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/55aaf5777235a06512542a72b045154c/transformed/jetified-core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2b5038cf32a6108ac7f0cc7e43387035/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2b5038cf32a6108ac7f0cc7e43387035/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a67d3f13690133ff569e5f737bc54e60/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a67d3f13690133ff569e5f737bc54e60/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/30c0766264951ffb9c5db8cf2344b538/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/30c0766264951ffb9c5db8cf2344b538/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/9f2623c0f62b6ccf5381bd0fbcea8598/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/9f2623c0f62b6ccf5381bd0fbcea8598/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/f577177c9d6a2b56675f36ac9bbf280e/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/f577177c9d6a2b56675f36ac9bbf280e/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/14ac4d8b22d77d4a235e2eedc62708fb/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/14ac4d8b22d77d4a235e2eedc62708fb/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/c3a60f3d5b06e7db24933c33e60e5097/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/c3a60f3d5b06e7db24933c33e60e5097/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/ba056e60dda6bab90ac471869598eb26/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/ba056e60dda6bab90ac471869598eb26/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/649d287516b5661989578d4cebde7789/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/649d287516b5661989578d4cebde7789/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/transforms-3/1f961c48afa6d74fe2b97d9d6f812d0b/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/transforms-3/1f961c48afa6d74fe2b97d9d6f812d0b/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/4d60d1fbffe4e1b983e5cc431794c185/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/4d60d1fbffe4e1b983e5cc431794c185/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/22419efcbd8d3b33f3ce8432e8d488c1/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/22419efcbd8d3b33f3ce8432e8d488c1/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9fe4fa9274742482c22e976791085b00/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9fe4fa9274742482c22e976791085b00/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/16381cb1f12da9192016d2bfdca490f6/transformed/jetified-exoplayer-datasource-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/16381cb1f12da9192016d2bfdca490f6/transformed/jetified-exoplayer-datasource-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/c5787bcf3352420dc1a99552536e105a/transformed/jetified-exoplayer-extractor-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/c5787bcf3352420dc1a99552536e105a/transformed/jetified-exoplayer-extractor-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/0ae9658b5ffe02d9cf7909a656958fc3/transformed/jetified-exoplayer-decoder-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/0ae9658b5ffe02d9cf7909a656958fc3/transformed/jetified-exoplayer-decoder-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2bee83a12b8347587af90ee7bdb5d837/transformed/jetified-exoplayer-database-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2bee83a12b8347587af90ee7bdb5d837/transformed/jetified-exoplayer-database-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/697f85aad62134b0b82754019dfe760f/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/697f85aad62134b0b82754019dfe760f/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/d916136341eee04a01bd9bdcfa44eaa8/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/d916136341eee04a01bd9bdcfa44eaa8/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f16fafad3502f8ace2464139725fd20c/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f16fafad3502f8ace2464139725fd20c/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/319a802935da1bbd3f801985fd3fd4d0/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/319a802935da1bbd3f801985fd3fd4d0/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/cf17373be7e9273a3ff8c8aa25e41959/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/cf17373be7e9273a3ff8c8aa25e41959/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.57.2] /Users/<USER>/.gradle/caches/transforms-3/e560e1f246dc5b9b3d5b180f89d2b798/transformed/jetified-grpc-android-1.57.2/AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] /Users/<USER>/.gradle/caches/transforms-3/e560e1f246dc5b9b3d5b180f89d2b798/transformed/jetified-grpc-android-1.57.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2cbb59da91b309ca5269bddaf67dff9/transformed/jetified-protolite-well-known-types-18.0.0/AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2cbb59da91b309ca5269bddaf67dff9/transformed/jetified-protolite-well-known-types-18.0.0/AndroidManifest.xml:7:5-9:41
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:6:5-66
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:23:5-67
	android:name
		ADDED from /Users/<USER>/Documents/Programming/BookBox/web_app/android/app/src/debug/AndroidManifest.xml:6:22-64
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:14:9-23:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:10:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
	android:name
		ADDED from [:firebase_auth] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_auth/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
	android:name
		ADDED from [:firebase_storage] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
	android:name
		ADDED from [:cloud_firestore] /Users/<USER>/Documents/Programming/BookBox/web_app/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/Documents/Programming/BookBox/web_app/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-124
queries
ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-19:15
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-18:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-72
	android:name
		ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:15:21-69
data
ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-44
	android:mimeType
		ADDED from [:file_picker] /Users/<USER>/Documents/Programming/BookBox/web_app/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:17:19-41
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-18:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-17:75
	android:resource
		ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:17:17-72
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Documents/Programming/BookBox/web_app/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:17-67
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/2c043d1f76191d97d76c674730356158/transformed/jetified-exoplayer-core-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/697f85aad62134b0b82754019dfe760f/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] /Users/<USER>/.gradle/caches/transforms-3/697f85aad62134b0b82754019dfe760f/transformed/jetified-exoplayer-common-2.18.7/AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.57.2] /Users/<USER>/.gradle/caches/transforms-3/e560e1f246dc5b9b3d5b180f89d2b798/transformed/jetified-grpc-android-1.57.2/AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] /Users/<USER>/.gradle/caches/transforms-3/e560e1f246dc5b9b3d5b180f89d2b798/transformed/jetified-grpc-android-1.57.2/AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/transforms-3/fb380c0e53d8d13a5d97c2fbae7b836c/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:20.3.0] /Users/<USER>/.gradle/caches/transforms-3/2442692cf4d2f2b6123298ab0877fd98/transformed/jetified-firebase-storage-20.3.0/AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] /Users/<USER>/.gradle/caches/transforms-3/f1199321a7883653f9099571b1fb8a50/transformed/jetified-firebase-firestore-24.11.0/AndroidManifest.xml:21:17-111
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:24:22-65
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/33667199917382787ea3f67f8d360d0a/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/e5e05fd5a2710cbbfa61efd119fdfa46/transformed/jetified-play-services-measurement-sdk-api-21.6.1/AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/a01dd68403457c8092920d7b0570c6d6/transformed/jetified-play-services-measurement-api-21.6.1/AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:17.1.2] /Users/<USER>/.gradle/caches/transforms-3/a9b26f259d55d75951abbf5717bc210d/transformed/jetified-firebase-appcheck-17.1.2/AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/93b127d75a1c15118edeab749b46004a/transformed/jetified-firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/8c083acde38d042d938adb1cea66dae0/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/transforms-3/230ad1ec0add1038b03e6cc5b9cd9f92/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/transforms-3/d47c0d9bf227fd11995cc3aba41aac84/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:22-95
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/5c85b7c3e2f0cff413ce63849593ace6/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:34:13-89
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/322d4f504a1e27dfca343ab21e83f8b2/transformed/jetified-play-services-measurement-impl-21.6.1/AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] /Users/<USER>/.gradle/caches/transforms-3/ae461c983147acd63c5feac05f22bd5f/transformed/jetified-play-services-measurement-21.6.1/AndroidManifest.xml:40:13-87
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e79b31419faadd50da9f4e321cfd1851/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/c13ac0acb99a4d426e4dbbac35bb5446/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] /Users/<USER>/.gradle/caches/transforms-3/220b8152a5049bd4fe4ba366d2ea660d/transformed/jetified-window-1.0.0-beta04/AndroidManifest.xml:29:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/transforms-3/741e58546a06835bd5fc6a974a095359/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
permission#com.example.web_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.web_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/df4275f1d170c4bf489f9fac49f83d9e/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7bb154727e425f4bf9cbbc2d29601c9d/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/ce1eecf85b48e9980da88ce75e2b7fb9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
