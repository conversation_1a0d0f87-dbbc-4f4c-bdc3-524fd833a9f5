{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.16.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.0+1/", "native_build": true, "dependencies": []}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.19.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.30.0/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.7.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.5/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.9+2/", "native_build": true, "dependencies": []}, {"name": "pdfx", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.6.0/", "native_build": true, "dependencies": ["device_info_plus"]}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.5.7/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.16.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.0+1/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.19.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.30.0/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.7.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.19/", "native_build": true, "dependencies": []}, {"name": "google_sign_in_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.23/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.9+6/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "pdfx", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.6.0/", "native_build": true, "dependencies": ["device_info_plus"]}, {"name": "video_player_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.4.13/", "native_build": true, "dependencies": []}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.16.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3/", "native_build": true, "dependencies": []}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.19.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.30.0/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.7.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.5/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "pdfx", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.6.0/", "native_build": true, "dependencies": ["device_info_plus"]}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.5.7/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_linux"]}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.16.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/", "native_build": true, "dependencies": []}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.19.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.30.0/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.7.1/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "pdfx", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.6.0/", "native_build": true, "dependencies": ["device_info_plus"]}], "web": [{"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.11.1/", "dependencies": ["firebase_core_web"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "dependencies": []}, {"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.0.0+1/", "dependencies": []}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.11.1/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.15.0/", "dependencies": []}, {"name": "firebase_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.9.1/", "dependencies": ["firebase_core_web"]}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/", "dependencies": []}, {"name": "pdfx", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdfx-2.6.0/", "dependencies": ["device_info_plus"]}, {"name": "video_player_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.0/", "dependencies": []}]}, "dependencyGraph": [{"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_storage", "dependencies": ["firebase_core", "firebase_storage_web"]}, {"name": "firebase_storage_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "pdfx", "dependencies": ["device_info_plus"]}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}], "date_created": "2024-04-27 22:22:34.664364", "version": "3.19.5"}